# SplitSecond Spark - 功能详细说明

## 📋 功能概览

本文档详细介绍了SplitSecond Spark游戏中实现的各项功能特性。

## 🔐 多账号系统

### 核心功能
- **账号创建**：支持创建多个独立的玩家账号
- **账号切换**：快速在不同账号间切换
- **账号删除**：安全删除不需要的账号
- **数据隔离**：每个账号的游戏数据完全独立

### 账号数据
每个账号包含以下数据：
- 账号基本信息（名称、描述、创建时间）
- 游戏进度和存档
- 个人最高分数记录
- 游戏设置和偏好
- 成就和统计数据

### 导入导出功能
- **账号导出**：将账号数据导出为JSON文件
- **账号导入**：从JSON文件恢复账号数据
- **批量操作**：支持多个账号的批量导入导出

## 🏆 排行榜系统

### 排行榜类型
1. **全球排行榜**：所有账号的最高分数排名
2. **每日排行榜**：当日最佳成绩排名（每日重置）
3. **每周排行榜**：本周最佳成绩排名（每周重置）
4. **个人排行榜**：个人历史最佳成绩记录

### 分数记录
- **自动提交**：游戏结束时自动提交分数
- **详细记录**：包含分数、游戏时间、关卡信息等
- **排名计算**：实时计算和更新排名
- **数据清理**：自动清理过期的排行榜数据

### 排行榜界面
- **标签页切换**：在不同排行榜类型间切换
- **排名显示**：显示排名、玩家名称、分数、时间等信息
- **个人高亮**：突出显示当前玩家的排名
- **刷新功能**：手动刷新排行榜数据

## 🎮 自定义关卡系统

### 关卡管理
- **关卡创建**：创建新的自定义关卡
- **关卡编辑**：修改现有关卡的内容
- **关卡保存**：将关卡数据保存到本地存储
- **关卡加载**：从存储中加载关卡数据
- **关卡删除**：删除不需要的关卡

### 关卡分享
- **关卡发布**：将关卡设为公开，供其他玩家游玩
- **关卡下载**：下载其他玩家分享的关卡
- **关卡评分**：为关卡评分（1-5星）
- **关卡搜索**：按名称、作者、难度等条件搜索关卡

### 关卡数据结构
每个关卡包含：
- **基本信息**：名称、描述、作者、难度等级
- **游戏数据**：平台、火花、障碍物等游戏对象
- **时间维度**：不同时间维度的对象配置
- **胜利条件**：所需火花数量、时间限制等
- **统计信息**：游玩次数、评分、创建时间等

## 🛠️ 关卡编辑器

### 编辑工具
1. **选择工具**：选择和移动关卡对象
2. **平台工具**：创建可站立的平台
3. **火花工具**：放置能量火花
4. **障碍物工具**：创建危险的障碍物
5. **传送门工具**：创建传送门对象
6. **触发器工具**：创建触发区域
7. **起始点工具**：设置玩家起始位置
8. **橡皮擦工具**：删除不需要的对象

### 编辑功能
- **网格对齐**：对象自动对齐到网格
- **缩放视图**：放大缩小编辑区域
- **拖拽移动**：直接拖拽移动对象
- **复制粘贴**：快速复制对象
- **撤销重做**：撤销和重做编辑操作

### 时间维度编辑
- **维度切换**：在过去、现在、未来间切换
- **独立编辑**：每个维度独立编辑对象
- **维度预览**：预览不同维度的关卡状态
- **维度同步**：某些对象可在多个维度中同步

### 快捷键支持
- **工具选择**：数字键1-8快速选择工具
- **维度切换**：Q/W/E键切换时间维度
- **编辑操作**：Ctrl+C复制、Ctrl+V粘贴、Delete删除
- **视图控制**：G键切换网格显示
- **保存测试**：Ctrl+S保存关卡

## 💾 自定义存储系统

### 存储架构
- **KV存储接口**：统一的键值存储抽象层
- **适配器模式**：支持多种存储后端
- **LocalStorage适配器**：基于浏览器LocalStorage
- **IndexedDB适配器**：基于浏览器IndexedDB，支持更大容量

### 存储功能
- **数据存储**：put(key, value) - 存储键值对
- **数据读取**：get(key) - 读取指定键的值
- **数据删除**：delete(key) - 删除指定键
- **数据列表**：list(prefix) - 列出指定前缀的所有键
- **数据清空**：clear() - 清空所有数据

### 数据管理
- **命名空间**：使用前缀区分不同类型的数据
- **数据序列化**：自动处理JSON序列化和反序列化
- **错误处理**：完善的错误处理和恢复机制
- **性能优化**：缓存和批量操作优化

## 🎨 用户界面

### 界面设计
- **现代化设计**：采用玻璃态（Glassmorphism）设计风格
- **响应式布局**：适配不同屏幕尺寸
- **动画效果**：流畅的过渡和交互动画
- **主题一致性**：统一的颜色和样式主题

### 交互体验
- **直观操作**：简单易懂的操作方式
- **快捷键支持**：提高操作效率
- **状态反馈**：清晰的操作状态提示
- **错误处理**：友好的错误信息显示

### 界面组件
- **对话框系统**：统一的对话框组件
- **表单控件**：输入框、选择框、按钮等
- **列表组件**：账号列表、关卡列表、排行榜等
- **工具栏**：编辑器工具栏和操作按钮

## 🔧 技术实现

### 架构设计
- **模块化设计**：功能模块独立，便于维护
- **事件驱动**：基于事件的系统通信
- **异步处理**：使用Promise处理异步操作
- **错误处理**：完善的错误捕获和处理机制

### 性能优化
- **懒加载**：按需加载功能模块
- **缓存机制**：减少重复的数据操作
- **批量操作**：优化大量数据的处理
- **内存管理**：及时清理不需要的对象

### 兼容性
- **浏览器兼容**：支持主流现代浏览器
- **设备适配**：PC和移动设备均可使用
- **降级处理**：在不支持的环境中提供降级方案

## 📊 数据统计

### 游戏统计
- **游戏时间**：记录总游戏时间和单次游戏时间
- **分数统计**：最高分、平均分、总分等
- **关卡统计**：完成的关卡数量和通关时间
- **火花统计**：收集的火花数量和类型

### 用户行为
- **操作统计**：记录用户的操作习惯
- **功能使用**：统计各功能的使用频率
- **错误记录**：记录和分析错误情况
- **性能监控**：监控游戏性能指标

## 🚀 未来扩展

### 扩展性设计
- **插件架构**：支持功能插件扩展
- **API接口**：提供标准的API接口
- **配置系统**：灵活的配置管理
- **主题系统**：支持自定义主题

### 云端集成
- **云存储**：支持云端数据同步
- **在线排行榜**：全球在线排行榜
- **社交功能**：好友系统和社交分享
- **内容分发**：关卡内容的在线分发

---

*本文档持续更新，记录游戏功能的最新发展。*
