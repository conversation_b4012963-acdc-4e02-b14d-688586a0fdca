<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试 - SplitSecond Spark</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #ffffff;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        .test-button {
            margin: 5px;
            padding: 10px 15px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            font-family: monospace;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .info { color: #2196F3; }
    </style>
</head>
<body>
    <h1>SplitSecond Spark 功能测试</h1>
    
    <div class="test-section">
        <h2>KV存储服务测试</h2>
        <button class="test-button" onclick="testKVStorage()">测试KV存储</button>
        <div id="kv-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>账号管理测试</h2>
        <button class="test-button" onclick="testAccountManager()">测试账号管理</button>
        <div id="account-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>排行榜测试</h2>
        <button class="test-button" onclick="testLeaderboard()">测试排行榜</button>
        <div id="leaderboard-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>关卡管理测试</h2>
        <button class="test-button" onclick="testLevelManager()">测试关卡管理</button>
        <div id="level-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>全面测试</h2>
        <button class="test-button" onclick="runAllTests()">运行所有测试</button>
        <div id="all-result" class="test-result"></div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/account-manager.js"></script>
    <script src="js/leaderboard.js"></script>
    <script src="js/level-manager.js"></script>
    
    <script>
        // 测试KV存储服务
        async function testKVStorage() {
            const result = document.getElementById('kv-result');
            result.innerHTML = '<span class="info">正在测试KV存储服务...</span>';
            
            try {
                const kvStorage = new KVStorageService();
                
                // 测试基本操作
                await kvStorage.put('test-key', 'test-value');
                const value = await kvStorage.get('test-key');
                
                if (value === 'test-value') {
                    result.innerHTML += '<br><span class="success">✓ 基本读写操作正常</span>';
                } else {
                    throw new Error('读写操作失败');
                }
                
                // 测试列表操作
                await kvStorage.put('test-key-2', 'test-value-2');
                const keys = await kvStorage.list('test-key');
                
                if (keys.length >= 2) {
                    result.innerHTML += '<br><span class="success">✓ 列表操作正常</span>';
                } else {
                    throw new Error('列表操作失败');
                }
                
                // 清理测试数据
                await kvStorage.delete('test-key');
                await kvStorage.delete('test-key-2');
                
                result.innerHTML += '<br><span class="success">✓ KV存储服务测试通过</span>';
                
            } catch (error) {
                result.innerHTML += `<br><span class="error">✗ KV存储服务测试失败: ${error.message}</span>`;
            }
        }
        
        // 测试账号管理
        async function testAccountManager() {
            const result = document.getElementById('account-result');
            result.innerHTML = '<span class="info">正在测试账号管理...</span>';
            
            try {
                const accountManager = new AccountManager();
                
                // 测试创建账号
                const accountId = await accountManager.createAccount('测试账号', '这是一个测试账号');
                
                if (accountId) {
                    result.innerHTML += '<br><span class="success">✓ 账号创建成功</span>';
                } else {
                    throw new Error('账号创建失败');
                }
                
                // 测试切换账号
                const switched = await accountManager.switchAccount(accountId);
                
                if (switched) {
                    result.innerHTML += '<br><span class="success">✓ 账号切换成功</span>';
                } else {
                    throw new Error('账号切换失败');
                }
                
                // 测试获取账号列表
                const accounts = await accountManager.getAccountList();
                
                if (accounts.length > 0) {
                    result.innerHTML += '<br><span class="success">✓ 账号列表获取成功</span>';
                } else {
                    throw new Error('账号列表获取失败');
                }
                
                result.innerHTML += '<br><span class="success">✓ 账号管理测试通过</span>';
                
            } catch (error) {
                result.innerHTML += `<br><span class="error">✗ 账号管理测试失败: ${error.message}</span>`;
            }
        }
        
        // 测试排行榜
        async function testLeaderboard() {
            const result = document.getElementById('leaderboard-result');
            result.innerHTML = '<span class="info">正在测试排行榜...</span>';
            
            try {
                const leaderboard = new LeaderboardManager();
                
                // 测试提交分数
                const submitted = await leaderboard.submitScore(1000, 60, 'test-level');
                
                if (submitted) {
                    result.innerHTML += '<br><span class="success">✓ 分数提交成功</span>';
                } else {
                    throw new Error('分数提交失败');
                }
                
                // 测试获取排行榜
                const globalRanking = await leaderboard.getGlobalRanking();
                
                if (Array.isArray(globalRanking)) {
                    result.innerHTML += '<br><span class="success">✓ 全球排行榜获取成功</span>';
                } else {
                    throw new Error('全球排行榜获取失败');
                }
                
                result.innerHTML += '<br><span class="success">✓ 排行榜测试通过</span>';
                
            } catch (error) {
                result.innerHTML += `<br><span class="error">✗ 排行榜测试失败: ${error.message}</span>`;
            }
        }
        
        // 测试关卡管理
        async function testLevelManager() {
            const result = document.getElementById('level-result');
            result.innerHTML = '<span class="info">正在测试关卡管理...</span>';
            
            try {
                const levelManager = new LevelManager();
                
                // 测试创建关卡
                const level = await levelManager.createLevel('测试关卡', '这是一个测试关卡');
                
                if (level && level.id) {
                    result.innerHTML += '<br><span class="success">✓ 关卡创建成功</span>';
                } else {
                    throw new Error('关卡创建失败');
                }
                
                // 测试保存关卡
                const saved = await levelManager.saveLevel(level.id, level);
                
                if (saved) {
                    result.innerHTML += '<br><span class="success">✓ 关卡保存成功</span>';
                } else {
                    throw new Error('关卡保存失败');
                }
                
                // 测试加载关卡
                const loaded = await levelManager.loadLevel(level.id);
                
                if (loaded && loaded.name === '测试关卡') {
                    result.innerHTML += '<br><span class="success">✓ 关卡加载成功</span>';
                } else {
                    throw new Error('关卡加载失败');
                }
                
                result.innerHTML += '<br><span class="success">✓ 关卡管理测试通过</span>';
                
            } catch (error) {
                result.innerHTML += `<br><span class="error">✗ 关卡管理测试失败: ${error.message}</span>`;
            }
        }
        
        // 运行所有测试
        async function runAllTests() {
            const result = document.getElementById('all-result');
            result.innerHTML = '<span class="info">正在运行所有测试...</span>';
            
            await testKVStorage();
            await testAccountManager();
            await testLeaderboard();
            await testLevelManager();
            
            result.innerHTML += '<br><span class="success">✓ 所有测试完成，请查看各个测试结果</span>';
        }
    </script>
</body>
</html>
