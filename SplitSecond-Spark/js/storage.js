/**
 * 自定义KV存储服务 - SplitSecond Spark
 * 提供统一的键值存储接口，支持多种后端适配器
 */

/**
 * 存储适配器基类
 * 定义所有存储后端必须实现的接口
 */
class StorageAdapter {
    /**
     * 保存键值对
     * @param {string} key - 键
     * @param {any} value - 值
     * @returns {Promise<boolean>} 是否成功
     */
    async put(key, value) {
        throw new Error('StorageAdapter.put() 必须被子类实现');
    }

    /**
     * 获取值
     * @param {string} key - 键
     * @returns {Promise<any>} 值或null
     */
    async get(key) {
        throw new Error('StorageAdapter.get() 必须被子类实现');
    }

    /**
     * 删除键值对
     * @param {string} key - 键
     * @returns {Promise<boolean>} 是否成功
     */
    async delete(key) {
        throw new Error('StorageAdapter.delete() 必须被子类实现');
    }

    /**
     * 列出指定前缀的所有键
     * @param {string} prefix - 前缀
     * @returns {Promise<Array<string>>} 键列表
     */
    async list(prefix = '') {
        throw new Error('StorageAdapter.list() 必须被子类实现');
    }

    /**
     * 清空所有数据
     * @returns {Promise<boolean>} 是否成功
     */
    async clear() {
        throw new Error('StorageAdapter.clear() 必须被子类实现');
    }
}

/**
 * LocalStorage适配器
 * 使用浏览器localStorage作为存储后端
 */
class LocalStorageAdapter extends StorageAdapter {
    constructor(prefix = 'splitsecond-spark') {
        super();
        this.prefix = prefix;
        this.separator = ':';
    }

    /**
     * 生成完整的存储键
     * @param {string} key - 原始键
     * @returns {string} 完整键
     */
    _getFullKey(key) {
        return `${this.prefix}${this.separator}${key}`;
    }

    /**
     * 从完整键中提取原始键
     * @param {string} fullKey - 完整键
     * @returns {string} 原始键
     */
    _getOriginalKey(fullKey) {
        const prefixWithSeparator = `${this.prefix}${this.separator}`;
        return fullKey.startsWith(prefixWithSeparator)
            ? fullKey.substring(prefixWithSeparator.length)
            : fullKey;
    }

    async put(key, value) {
        try {
            const fullKey = this._getFullKey(key);
            const serializedValue = JSON.stringify({
                value: value,
                timestamp: Date.now(),
                type: typeof value
            });
            localStorage.setItem(fullKey, serializedValue);
            return true;
        } catch (error) {
            DebugUtils.log(`LocalStorage存储失败: ${error.message}`, 'error');
            return false;
        }
    }

    async get(key) {
        try {
            const fullKey = this._getFullKey(key);
            const serializedValue = localStorage.getItem(fullKey);
            if (serializedValue === null) {
                return null;
            }
            const data = JSON.parse(serializedValue);
            return data.value;
        } catch (error) {
            DebugUtils.log(`LocalStorage读取失败: ${error.message}`, 'error');
            return null;
        }
    }

    async delete(key) {
        try {
            const fullKey = this._getFullKey(key);
            localStorage.removeItem(fullKey);
            return true;
        } catch (error) {
            DebugUtils.log(`LocalStorage删除失败: ${error.message}`, 'error');
            return false;
        }
    }

    async list(prefix = '') {
        try {
            const keys = [];
            const searchPrefix = this._getFullKey(prefix);

            for (let i = 0; i < localStorage.length; i++) {
                const fullKey = localStorage.key(i);
                if (fullKey && fullKey.startsWith(searchPrefix)) {
                    const originalKey = this._getOriginalKey(fullKey);
                    keys.push(originalKey);
                }
            }

            return keys.sort();
        } catch (error) {
            DebugUtils.log(`LocalStorage列表获取失败: ${error.message}`, 'error');
            return [];
        }
    }

    async clear() {
        try {
            const keysToRemove = [];
            const prefixWithSeparator = `${this.prefix}${this.separator}`;

            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(prefixWithSeparator)) {
                    keysToRemove.push(key);
                }
            }

            keysToRemove.forEach(key => localStorage.removeItem(key));
            return true;
        } catch (error) {
            DebugUtils.log(`LocalStorage清空失败: ${error.message}`, 'error');
            return false;
        }
    }
}

/**
 * IndexedDB适配器
 * 使用浏览器IndexedDB作为存储后端，支持更大的存储容量
 */
class IndexedDBAdapter extends StorageAdapter {
    constructor(dbName = 'SplitSecondSparkDB', version = 1) {
        super();
        this.dbName = dbName;
        this.version = version;
        this.storeName = 'keyValueStore';
        this.db = null;
        this.initialized = false;
    }

    /**
     * 初始化IndexedDB连接
     * @returns {Promise<boolean>} 是否成功初始化
     */
    async initialize() {
        if (this.initialized) return true;

        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => {
                DebugUtils.log('IndexedDB初始化失败', 'error');
                reject(false);
            };

            request.onsuccess = (event) => {
                this.db = event.target.result;
                this.initialized = true;
                DebugUtils.log('IndexedDB初始化成功');
                resolve(true);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains(this.storeName)) {
                    const store = db.createObjectStore(this.storeName, { keyPath: 'key' });
                    store.createIndex('timestamp', 'timestamp', { unique: false });
                }
            };
        });
    }

    async put(key, value) {
        if (!this.initialized && !(await this.initialize())) {
            return false;
        }

        return new Promise((resolve) => {
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);

            const data = {
                key: key,
                value: value,
                timestamp: Date.now(),
                type: typeof value
            };

            const request = store.put(data);

            request.onsuccess = () => resolve(true);
            request.onerror = () => {
                DebugUtils.log(`IndexedDB存储失败: ${key}`, 'error');
                resolve(false);
            };
        });
    }

    async get(key) {
        if (!this.initialized && !(await this.initialize())) {
            return null;
        }

        return new Promise((resolve) => {
            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.get(key);

            request.onsuccess = (event) => {
                const result = event.target.result;
                resolve(result ? result.value : null);
            };

            request.onerror = () => {
                DebugUtils.log(`IndexedDB读取失败: ${key}`, 'error');
                resolve(null);
            };
        });
    }

    async delete(key) {
        if (!this.initialized && !(await this.initialize())) {
            return false;
        }

        return new Promise((resolve) => {
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.delete(key);

            request.onsuccess = () => resolve(true);
            request.onerror = () => {
                DebugUtils.log(`IndexedDB删除失败: ${key}`, 'error');
                resolve(false);
            };
        });
    }

    async list(prefix = '') {
        if (!this.initialized && !(await this.initialize())) {
            return [];
        }

        return new Promise((resolve) => {
            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.getAllKeys();

            request.onsuccess = (event) => {
                const keys = event.target.result || [];
                const filteredKeys = prefix
                    ? keys.filter(key => key.startsWith(prefix))
                    : keys;
                resolve(filteredKeys.sort());
            };

            request.onerror = () => {
                DebugUtils.log('IndexedDB列表获取失败', 'error');
                resolve([]);
            };
        });
    }

    async clear() {
        if (!this.initialized && !(await this.initialize())) {
            return false;
        }

        return new Promise((resolve) => {
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.clear();

            request.onsuccess = () => resolve(true);
            request.onerror = () => {
                DebugUtils.log('IndexedDB清空失败', 'error');
                resolve(false);
            };
        });
    }
}

/**
 * KV存储服务
 * 提供统一的键值存储接口，支持多种后端适配器
 */
class KVStorageService {
    constructor(adapter = null) {
        // 默认使用LocalStorage适配器
        this.adapter = adapter || new LocalStorageAdapter();
        this.initialized = false;

        DebugUtils.log('KV存储服务初始化完成');
    }

    /**
     * 初始化存储服务
     * @returns {Promise<boolean>} 是否成功初始化
     */
    async initialize() {
        if (this.initialized) return true;

        try {
            // 如果适配器有初始化方法，则调用
            if (typeof this.adapter.initialize === 'function') {
                await this.adapter.initialize();
            }
            this.initialized = true;
            return true;
        } catch (error) {
            DebugUtils.log(`KV存储服务初始化失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 保存键值对
     * @param {string} key - 键
     * @param {any} value - 值
     * @returns {Promise<boolean>} 是否成功
     */
    async put(key, value) {
        if (!this.initialized && !(await this.initialize())) {
            return false;
        }

        try {
            return await this.adapter.put(key, value);
        } catch (error) {
            DebugUtils.log(`KV存储put操作失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 获取值
     * @param {string} key - 键
     * @returns {Promise<any>} 值或null
     */
    async get(key) {
        if (!this.initialized && !(await this.initialize())) {
            return null;
        }

        try {
            return await this.adapter.get(key);
        } catch (error) {
            DebugUtils.log(`KV存储get操作失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 删除键值对
     * @param {string} key - 键
     * @returns {Promise<boolean>} 是否成功
     */
    async delete(key) {
        if (!this.initialized && !(await this.initialize())) {
            return false;
        }

        try {
            return await this.adapter.delete(key);
        } catch (error) {
            DebugUtils.log(`KV存储delete操作失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 列出指定前缀的所有键
     * @param {string} prefix - 前缀
     * @returns {Promise<Array<string>>} 键列表
     */
    async list(prefix = '') {
        if (!this.initialized && !(await this.initialize())) {
            return [];
        }

        try {
            return await this.adapter.list(prefix);
        } catch (error) {
            DebugUtils.log(`KV存储list操作失败: ${error.message}`, 'error');
            return [];
        }
    }

    /**
     * 清空所有数据
     * @returns {Promise<boolean>} 是否成功
     */
    async clear() {
        if (!this.initialized && !(await this.initialize())) {
            return false;
        }

        try {
            return await this.adapter.clear();
        } catch (error) {
            DebugUtils.log(`KV存储clear操作失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 切换存储适配器
     * @param {StorageAdapter} newAdapter - 新的存储适配器
     * @returns {Promise<boolean>} 是否成功切换
     */
    async switchAdapter(newAdapter) {
        try {
            this.adapter = newAdapter;
            this.initialized = false;
            return await this.initialize();
        } catch (error) {
            DebugUtils.log(`存储适配器切换失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 获取存储统计信息
     * @returns {Promise<object>} 存储统计信息
     */
    async getStorageStats() {
        try {
            const keys = await this.list();
            const stats = {
                totalKeys: keys.length,
                adapterType: this.adapter.constructor.name,
                initialized: this.initialized
            };

            // 如果是LocalStorage，计算存储大小
            if (this.adapter instanceof LocalStorageAdapter) {
                let totalSize = 0;
                for (const key of keys) {
                    const value = await this.get(key);
                    if (value !== null) {
                        totalSize += JSON.stringify(value).length;
                    }
                }
                stats.totalSize = totalSize;
                stats.estimatedCapacity = 5 * 1024 * 1024; // 5MB
                stats.usagePercentage = (totalSize / stats.estimatedCapacity) * 100;
            }

            return stats;
        } catch (error) {
            DebugUtils.log(`获取存储统计失败: ${error.message}`, 'error');
            return {
                totalKeys: 0,
                adapterType: 'unknown',
                initialized: false
            };
        }
    }
}

/**
 * 游戏存储管理器（兼容性包装类）
 * 基于KV存储服务实现，保持向后兼容性
 */
class GameStorage {
    constructor(kvService = null) {
        // 使用传入的KV服务或创建默认的
        this.kvService = kvService || new KVStorageService();

        // 存储键定义
        this.storageKeys = {
            gameData: 'game:save-data',
            settings: 'game:settings',
            accounts: 'accounts:list',
            currentAccount: 'accounts:current',
            leaderboard: 'leaderboard:scores',
            customLevels: 'levels:custom'
        };

        this.defaultSettings = {
            musicVolume: 70,
            sfxVolume: 80,
            graphicsQuality: 'medium',
            language: 'zh-CN'
        };

        // 初始化
        this.initialize();

        DebugUtils.log('游戏存储管理器初始化完成');
    }

    /**
     * 初始化存储管理器
     */
    async initialize() {
        await this.kvService.initialize();
        await this.initializeSettings();
    }

    /**
     * 初始化游戏设置
     */
    async initializeSettings() {
        const savedSettings = await this.loadSettings();
        if (!savedSettings) {
            await this.saveSettings(this.defaultSettings);
            DebugUtils.log('创建默认游戏设置');
        }
    }

    /**
     * 保存游戏数据
     * @param {object} gameData - 游戏数据对象
     * @param {string} accountId - 账号ID（可选）
     * @returns {Promise<boolean>} 保存是否成功
     */
    async saveGame(gameData, accountId = null) {
        try {
            const saveData = {
                version: '1.0.0',
                timestamp: Date.now(),
                data: gameData
            };

            const key = accountId
                ? `accounts:${accountId}:save-data`
                : this.storageKeys.gameData;

            const success = await this.kvService.put(key, saveData);

            if (success) {
                DebugUtils.log('游戏数据保存成功');
            }
            return success;
        } catch (error) {
            DebugUtils.log(`游戏数据保存失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 加载游戏数据
     * @param {string} accountId - 账号ID（可选）
     * @returns {Promise<object|null>} 游戏数据对象或null
     */
    async loadGame(accountId = null) {
        try {
            const key = accountId
                ? `accounts:${accountId}:save-data`
                : this.storageKeys.gameData;

            const saveData = await this.kvService.get(key);
            if (!saveData) {
                DebugUtils.log('未找到保存的游戏数据');
                return null;
            }

            // 验证数据格式
            if (!saveData.version || !saveData.data) {
                DebugUtils.log('游戏数据格式无效', 'warn');
                return null;
            }

            DebugUtils.log('游戏数据加载成功');
            return saveData.data;
        } catch (error) {
            DebugUtils.log(`游戏数据加载失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 检查是否存在保存的游戏数据
     * @param {string} accountId - 账号ID（可选）
     * @returns {Promise<boolean>} 是否存在保存数据
     */
    async hasSavedGame(accountId = null) {
        try {
            const key = accountId
                ? `accounts:${accountId}:save-data`
                : this.storageKeys.gameData;

            const data = await this.kvService.get(key);
            return data !== null;
        } catch (error) {
            DebugUtils.log(`检查保存数据失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 删除保存的游戏数据
     * @param {string} accountId - 账号ID（可选）
     * @returns {Promise<boolean>} 删除是否成功
     */
    async deleteSavedGame(accountId = null) {
        try {
            const key = accountId
                ? `accounts:${accountId}:save-data`
                : this.storageKeys.gameData;

            const success = await this.kvService.delete(key);
            if (success) {
                DebugUtils.log('游戏数据删除成功');
            }
            return success;
        } catch (error) {
            DebugUtils.log(`游戏数据删除失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 保存游戏设置
     * @param {object} settings - 设置对象
     * @param {string} accountId - 账号ID（可选）
     * @returns {Promise<boolean>} 保存是否成功
     */
    async saveSettings(settings, accountId = null) {
        try {
            const settingsData = {
                ...this.defaultSettings,
                ...settings,
                timestamp: Date.now()
            };

            const key = accountId
                ? `accounts:${accountId}:settings`
                : this.storageKeys.settings;

            const success = await this.kvService.put(key, settingsData);

            if (success) {
                DebugUtils.log('游戏设置保存成功');
            }
            return success;
        } catch (error) {
            DebugUtils.log(`游戏设置保存失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 加载游戏设置
     * @param {string} accountId - 账号ID（可选）
     * @returns {Promise<object>} 设置对象
     */
    async loadSettings(accountId = null) {
        try {
            const key = accountId
                ? `accounts:${accountId}:settings`
                : this.storageKeys.settings;

            const settings = await this.kvService.get(key);
            if (!settings) {
                return this.defaultSettings;
            }

            // 合并默认设置，确保所有设置项都存在
            const mergedSettings = {
                ...this.defaultSettings,
                ...settings
            };

            DebugUtils.log('游戏设置加载成功');
            return mergedSettings;
        } catch (error) {
            DebugUtils.log(`游戏设置加载失败: ${error.message}`, 'error');
            return this.defaultSettings;
        }
    }

    /**
     * 重置游戏设置为默认值
     * @param {string} accountId - 账号ID（可选）
     * @returns {Promise<boolean>} 重置是否成功
     */
    async resetSettings(accountId = null) {
        return await this.saveSettings(this.defaultSettings, accountId);
    }

    /**
     * 获取存储使用情况
     * @returns {Promise<object>} 存储使用情况对象
     */
    async getStorageInfo() {
        try {
            const stats = await this.kvService.getStorageStats();
            const hasGameData = await this.hasSavedGame();

            return {
                ...stats,
                hasGameData
            };
        } catch (error) {
            DebugUtils.log(`获取存储信息失败: ${error.message}`, 'error');
            return {
                totalKeys: 0,
                adapterType: 'unknown',
                initialized: false,
                hasGameData: false
            };
        }
    }

    /**
     * 获取KV存储服务实例
     * @returns {KVStorageService} KV存储服务
     */
    getKVService() {
        return this.kvService;
    }

    /**
     * 切换存储适配器
     * @param {StorageAdapter} adapter - 新的存储适配器
     * @returns {Promise<boolean>} 是否成功切换
     */
    async switchStorageAdapter(adapter) {
        try {
            return await this.kvService.switchAdapter(adapter);
        } catch (error) {
            DebugUtils.log(`切换存储适配器失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 导出游戏数据（用于备份）
     * @param {string} accountId - 账号ID（可选）
     * @returns {Promise<string|null>} 导出的JSON字符串
     */
    async exportGameData(accountId = null) {
        try {
            const gameData = await this.loadGame(accountId);
            const settings = await this.loadSettings(accountId);

            if (!gameData) {
                DebugUtils.log('没有游戏数据可导出', 'warn');
                return null;
            }

            const exportData = {
                version: '1.0.0',
                exportTime: Date.now(),
                accountId,
                gameData,
                settings
            };

            const exportString = JSON.stringify(exportData, null, 2);
            DebugUtils.log('游戏数据导出成功');
            return exportString;
        } catch (error) {
            DebugUtils.log(`游戏数据导出失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 导入游戏数据（用于恢复备份）
     * @param {string} importString - 导入的JSON字符串
     * @param {string} targetAccountId - 目标账号ID（可选）
     * @returns {Promise<boolean>} 导入是否成功
     */
    async importGameData(importString, targetAccountId = null) {
        try {
            const importData = JSON.parse(importString);

            // 验证导入数据格式
            if (!importData.version || !importData.gameData) {
                DebugUtils.log('导入数据格式无效', 'error');
                return false;
            }

            const accountId = targetAccountId || importData.accountId;

            // 保存游戏数据
            const gameSuccess = await this.saveGame(importData.gameData, accountId);

            // 保存设置（如果存在）
            let settingsSuccess = true;
            if (importData.settings) {
                settingsSuccess = await this.saveSettings(importData.settings, accountId);
            }

            if (gameSuccess && settingsSuccess) {
                DebugUtils.log('游戏数据导入成功');
                return true;
            } else {
                DebugUtils.log('游戏数据导入部分失败', 'warn');
                return false;
            }
        } catch (error) {
            DebugUtils.log(`游戏数据导入失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 清理所有存储数据
     * @returns {Promise<boolean>} 清理是否成功
     */
    async clearAllData() {
        try {
            const success = await this.kvService.clear();
            if (success) {
                DebugUtils.log('所有存储数据清理完成');
            }
            return success;
        } catch (error) {
            DebugUtils.log(`存储数据清理失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 创建游戏数据的快照
     * @param {object} gameData - 游戏数据
     * @returns {object} 数据快照
     */
    createSnapshot(gameData) {
        return {
            timestamp: Date.now(),
            data: JSON.parse(JSON.stringify(gameData)) // 深拷贝
        };
    }
}

// 创建全局存储管理器实例
// 默认使用LocalStorage适配器，可以通过switchStorageAdapter方法切换
window.gameStorage = new GameStorage();

// 创建全局KV存储服务实例（用于直接访问KV接口）
window.kvStorage = window.gameStorage.getKVService();

// 导出存储适配器类，供外部扩展使用
window.StorageAdapter = StorageAdapter;
window.LocalStorageAdapter = LocalStorageAdapter;
window.IndexedDBAdapter = IndexedDBAdapter;
window.KVStorageService = KVStorageService;

// 创建全局存储管理器实例
window.gameStorage = new GameStorage();
