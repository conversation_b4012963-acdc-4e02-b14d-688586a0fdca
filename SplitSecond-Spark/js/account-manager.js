/**
 * 玩家账号管理系统 - SplitSecond Spark
 * 基于KV存储实现多账号管理，支持账号创建、切换、删除等功能
 */

class AccountManager {
    constructor(kvService) {
        this.kvService = kvService || window.kvStorage;
        this.currentAccountId = null;
        
        // 存储键定义
        this.storageKeys = {
            accountsList: 'accounts:list',
            currentAccount: 'accounts:current',
            accountPrefix: 'accounts:'
        };

        // 默认账号数据结构
        this.defaultAccountData = {
            id: null,
            name: '',
            avatar: 'default',
            createdAt: null,
            lastLoginAt: null,
            gameStats: {
                totalPlayTime: 0,
                totalSparks: 0,
                bestScore: 0,
                levelsCompleted: 0,
                dimensionSwitches: 0
            },
            achievements: [],
            preferences: {
                difficulty: 'normal',
                theme: 'default'
            }
        };

        this.initialize();
        DebugUtils.log('玩家账号管理系统初始化完成');
    }

    /**
     * 初始化账号管理系统
     */
    async initialize() {
        try {
            // 加载当前账号
            const currentAccountId = await this.kvService.get(this.storageKeys.currentAccount);
            if (currentAccountId) {
                const account = await this.getAccount(currentAccountId);
                if (account) {
                    this.currentAccountId = currentAccountId;
                    DebugUtils.log(`当前账号: ${account.name} (${currentAccountId})`);
                } else {
                    // 当前账号不存在，清除记录
                    await this.kvService.delete(this.storageKeys.currentAccount);
                }
            }

            // 如果没有当前账号，尝试创建默认账号
            if (!this.currentAccountId) {
                const accounts = await this.getAccountsList();
                if (accounts.length === 0) {
                    // 创建默认账号
                    const defaultAccount = await this.createAccount('默认玩家');
                    if (defaultAccount) {
                        await this.switchAccount(defaultAccount.id);
                        DebugUtils.log('创建并切换到默认账号');
                    }
                } else {
                    // 切换到第一个账号
                    await this.switchAccount(accounts[0].id);
                    DebugUtils.log('切换到第一个可用账号');
                }
            }
        } catch (error) {
            DebugUtils.log(`账号管理系统初始化失败: ${error.message}`, 'error');
        }
    }

    /**
     * 生成唯一的账号ID
     * @returns {string} 账号ID
     */
    generateAccountId() {
        return 'account_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 创建新账号
     * @param {string} name - 账号名称
     * @param {string} avatar - 头像标识
     * @returns {Promise<object|null>} 创建的账号对象或null
     */
    async createAccount(name, avatar = 'default') {
        try {
            // 验证账号名称
            if (!name || name.trim().length === 0) {
                DebugUtils.log('账号名称不能为空', 'error');
                return null;
            }

            // 检查名称是否已存在
            const existingAccounts = await this.getAccountsList();
            const nameExists = existingAccounts.some(account => 
                account.name.toLowerCase() === name.trim().toLowerCase()
            );

            if (nameExists) {
                DebugUtils.log('账号名称已存在', 'error');
                return null;
            }

            // 创建账号数据
            const accountId = this.generateAccountId();
            const accountData = {
                ...this.defaultAccountData,
                id: accountId,
                name: name.trim(),
                avatar: avatar,
                createdAt: Date.now(),
                lastLoginAt: Date.now()
            };

            // 保存账号数据
            const accountKey = `${this.storageKeys.accountPrefix}${accountId}:profile`;
            const success = await this.kvService.put(accountKey, accountData);

            if (!success) {
                DebugUtils.log('账号数据保存失败', 'error');
                return null;
            }

            // 更新账号列表
            await this.updateAccountsList();

            DebugUtils.log(`账号创建成功: ${name} (${accountId})`);
            return accountData;
        } catch (error) {
            DebugUtils.log(`创建账号失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 获取账号信息
     * @param {string} accountId - 账号ID
     * @returns {Promise<object|null>} 账号对象或null
     */
    async getAccount(accountId) {
        try {
            const accountKey = `${this.storageKeys.accountPrefix}${accountId}:profile`;
            const accountData = await this.kvService.get(accountKey);
            return accountData;
        } catch (error) {
            DebugUtils.log(`获取账号信息失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 更新账号信息
     * @param {string} accountId - 账号ID
     * @param {object} updates - 更新的数据
     * @returns {Promise<boolean>} 是否成功
     */
    async updateAccount(accountId, updates) {
        try {
            const account = await this.getAccount(accountId);
            if (!account) {
                DebugUtils.log('账号不存在', 'error');
                return false;
            }

            // 合并更新数据
            const updatedAccount = {
                ...account,
                ...updates,
                lastLoginAt: Date.now()
            };

            const accountKey = `${this.storageKeys.accountPrefix}${accountId}:profile`;
            const success = await this.kvService.put(accountKey, updatedAccount);

            if (success) {
                DebugUtils.log(`账号信息更新成功: ${accountId}`);
            }
            return success;
        } catch (error) {
            DebugUtils.log(`更新账号信息失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 删除账号
     * @param {string} accountId - 账号ID
     * @returns {Promise<boolean>} 是否成功
     */
    async deleteAccount(accountId) {
        try {
            // 不能删除当前账号
            if (accountId === this.currentAccountId) {
                DebugUtils.log('不能删除当前使用的账号', 'error');
                return false;
            }

            // 删除账号相关的所有数据
            const accountKeys = await this.kvService.list(`${this.storageKeys.accountPrefix}${accountId}:`);
            
            for (const key of accountKeys) {
                await this.kvService.delete(key);
            }

            // 更新账号列表
            await this.updateAccountsList();

            DebugUtils.log(`账号删除成功: ${accountId}`);
            return true;
        } catch (error) {
            DebugUtils.log(`删除账号失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 切换账号
     * @param {string} accountId - 目标账号ID
     * @returns {Promise<boolean>} 是否成功
     */
    async switchAccount(accountId) {
        try {
            const account = await this.getAccount(accountId);
            if (!account) {
                DebugUtils.log('目标账号不存在', 'error');
                return false;
            }

            // 更新当前账号
            this.currentAccountId = accountId;
            await this.kvService.put(this.storageKeys.currentAccount, accountId);

            // 更新最后登录时间
            await this.updateAccount(accountId, { lastLoginAt: Date.now() });

            DebugUtils.log(`切换到账号: ${account.name} (${accountId})`);
            return true;
        } catch (error) {
            DebugUtils.log(`切换账号失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 获取当前账号
     * @returns {Promise<object|null>} 当前账号对象或null
     */
    async getCurrentAccount() {
        if (!this.currentAccountId) {
            return null;
        }
        return await this.getAccount(this.currentAccountId);
    }

    /**
     * 获取当前账号ID
     * @returns {string|null} 当前账号ID或null
     */
    getCurrentAccountId() {
        return this.currentAccountId;
    }

    /**
     * 获取所有账号列表
     * @returns {Promise<Array>} 账号列表
     */
    async getAccountsList() {
        try {
            const accountKeys = await this.kvService.list(`${this.storageKeys.accountPrefix}`);
            const profileKeys = accountKeys.filter(key => key.endsWith(':profile'));
            
            const accounts = [];
            for (const key of profileKeys) {
                const account = await this.kvService.get(key);
                if (account) {
                    accounts.push(account);
                }
            }

            // 按创建时间排序
            accounts.sort((a, b) => a.createdAt - b.createdAt);
            return accounts;
        } catch (error) {
            DebugUtils.log(`获取账号列表失败: ${error.message}`, 'error');
            return [];
        }
    }

    /**
     * 更新账号列表缓存
     */
    async updateAccountsList() {
        try {
            const accounts = await this.getAccountsList();
            const accountsInfo = accounts.map(account => ({
                id: account.id,
                name: account.name,
                avatar: account.avatar,
                createdAt: account.createdAt,
                lastLoginAt: account.lastLoginAt
            }));

            await this.kvService.put(this.storageKeys.accountsList, accountsInfo);
        } catch (error) {
            DebugUtils.log(`更新账号列表失败: ${error.message}`, 'error');
        }
    }

    /**
     * 更新账号游戏统计
     * @param {string} accountId - 账号ID（可选，默认当前账号）
     * @param {object} stats - 统计数据更新
     * @returns {Promise<boolean>} 是否成功
     */
    async updateGameStats(accountId = null, stats) {
        try {
            const targetAccountId = accountId || this.currentAccountId;
            if (!targetAccountId) {
                DebugUtils.log('没有指定账号ID', 'error');
                return false;
            }

            const account = await this.getAccount(targetAccountId);
            if (!account) {
                DebugUtils.log('账号不存在', 'error');
                return false;
            }

            // 合并统计数据
            const updatedStats = {
                ...account.gameStats,
                ...stats
            };

            return await this.updateAccount(targetAccountId, { gameStats: updatedStats });
        } catch (error) {
            DebugUtils.log(`更新游戏统计失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 添加成就
     * @param {string} achievementId - 成就ID
     * @param {string} accountId - 账号ID（可选，默认当前账号）
     * @returns {Promise<boolean>} 是否成功
     */
    async addAchievement(achievementId, accountId = null) {
        try {
            const targetAccountId = accountId || this.currentAccountId;
            if (!targetAccountId) {
                DebugUtils.log('没有指定账号ID', 'error');
                return false;
            }

            const account = await this.getAccount(targetAccountId);
            if (!account) {
                DebugUtils.log('账号不存在', 'error');
                return false;
            }

            // 检查成就是否已存在
            if (account.achievements.includes(achievementId)) {
                DebugUtils.log('成就已存在', 'warn');
                return true;
            }

            // 添加成就
            const updatedAchievements = [...account.achievements, achievementId];
            return await this.updateAccount(targetAccountId, { achievements: updatedAchievements });
        } catch (error) {
            DebugUtils.log(`添加成就失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 获取账号的游戏数据
     * @param {string} accountId - 账号ID（可选，默认当前账号）
     * @returns {Promise<object|null>} 游戏数据或null
     */
    async getAccountGameData(accountId = null) {
        try {
            const targetAccountId = accountId || this.currentAccountId;
            if (!targetAccountId) {
                return null;
            }

            const gameDataKey = `${this.storageKeys.accountPrefix}${targetAccountId}:save-data`;
            return await this.kvService.get(gameDataKey);
        } catch (error) {
            DebugUtils.log(`获取账号游戏数据失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 保存账号的游戏数据
     * @param {object} gameData - 游戏数据
     * @param {string} accountId - 账号ID（可选，默认当前账号）
     * @returns {Promise<boolean>} 是否成功
     */
    async saveAccountGameData(gameData, accountId = null) {
        try {
            const targetAccountId = accountId || this.currentAccountId;
            if (!targetAccountId) {
                DebugUtils.log('没有指定账号ID', 'error');
                return false;
            }

            const saveData = {
                version: '1.0.0',
                timestamp: Date.now(),
                data: gameData
            };

            const gameDataKey = `${this.storageKeys.accountPrefix}${targetAccountId}:save-data`;
            return await this.kvService.put(gameDataKey, saveData);
        } catch (error) {
            DebugUtils.log(`保存账号游戏数据失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 获取账号的设置
     * @param {string} accountId - 账号ID（可选，默认当前账号）
     * @returns {Promise<object|null>} 设置对象或null
     */
    async getAccountSettings(accountId = null) {
        try {
            const targetAccountId = accountId || this.currentAccountId;
            if (!targetAccountId) {
                return null;
            }

            const settingsKey = `${this.storageKeys.accountPrefix}${targetAccountId}:settings`;
            return await this.kvService.get(settingsKey);
        } catch (error) {
            DebugUtils.log(`获取账号设置失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 保存账号的设置
     * @param {object} settings - 设置对象
     * @param {string} accountId - 账号ID（可选，默认当前账号）
     * @returns {Promise<boolean>} 是否成功
     */
    async saveAccountSettings(settings, accountId = null) {
        try {
            const targetAccountId = accountId || this.currentAccountId;
            if (!targetAccountId) {
                DebugUtils.log('没有指定账号ID', 'error');
                return false;
            }

            const settingsKey = `${this.storageKeys.accountPrefix}${targetAccountId}:settings`;
            return await this.kvService.put(settingsKey, settings);
        } catch (error) {
            DebugUtils.log(`保存账号设置失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 导出账号数据
     * @param {string} accountId - 账号ID（可选，默认当前账号）
     * @returns {Promise<string|null>} 导出的JSON字符串或null
     */
    async exportAccountData(accountId = null) {
        try {
            const targetAccountId = accountId || this.currentAccountId;
            if (!targetAccountId) {
                DebugUtils.log('没有指定账号ID', 'error');
                return null;
            }

            const account = await this.getAccount(targetAccountId);
            const gameData = await this.getAccountGameData(targetAccountId);
            const settings = await this.getAccountSettings(targetAccountId);

            if (!account) {
                DebugUtils.log('账号不存在', 'error');
                return null;
            }

            const exportData = {
                version: '1.0.0',
                exportTime: Date.now(),
                account: account,
                gameData: gameData,
                settings: settings
            };

            return JSON.stringify(exportData, null, 2);
        } catch (error) {
            DebugUtils.log(`导出账号数据失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 导入账号数据
     * @param {string} importString - 导入的JSON字符串
     * @returns {Promise<string|null>} 导入的账号ID或null
     */
    async importAccountData(importString) {
        try {
            const importData = JSON.parse(importString);

            if (!importData.account || !importData.version) {
                DebugUtils.log('导入数据格式无效', 'error');
                return null;
            }

            // 生成新的账号ID
            const newAccountId = this.generateAccountId();
            const accountData = {
                ...importData.account,
                id: newAccountId,
                createdAt: Date.now(),
                lastLoginAt: Date.now()
            };

            // 保存账号信息
            const accountKey = `${this.storageKeys.accountPrefix}${newAccountId}:profile`;
            await this.kvService.put(accountKey, accountData);

            // 保存游戏数据（如果存在）
            if (importData.gameData) {
                await this.saveAccountGameData(importData.gameData.data, newAccountId);
            }

            // 保存设置（如果存在）
            if (importData.settings) {
                await this.saveAccountSettings(importData.settings, newAccountId);
            }

            // 更新账号列表
            await this.updateAccountsList();

            DebugUtils.log(`账号数据导入成功: ${accountData.name} (${newAccountId})`);
            return newAccountId;
        } catch (error) {
            DebugUtils.log(`导入账号数据失败: ${error.message}`, 'error');
            return null;
        }
    }
}

// 创建全局账号管理器实例
window.accountManager = new AccountManager();
