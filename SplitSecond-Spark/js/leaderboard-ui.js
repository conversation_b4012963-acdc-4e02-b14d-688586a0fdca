/**
 * 排行榜UI控制器 - SplitSecond Spark
 * 处理排行榜界面的交互逻辑
 */

class LeaderboardUI {
    constructor() {
        this.leaderboardManager = window.leaderboardManager;
        this.accountManager = window.accountManager;
        this.currentTab = 'global';
        
        this.initializeEventListeners();
        
        DebugUtils.log('排行榜UI初始化完成');
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 主菜单按钮
        const leaderboardBtn = DOMUtils.$('#leaderboard-btn');
        if (leaderboardBtn) {
            leaderboardBtn.addEventListener('click', () => this.showLeaderboard());
        }

        // 排行榜界面按钮
        const leaderboardBackBtn = DOMUtils.$('#leaderboard-back-btn');
        if (leaderboardBackBtn) {
            leaderboardBackBtn.addEventListener('click', () => this.hideLeaderboard());
        }

        const refreshBtn = DOMUtils.$('#refresh-leaderboard-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshCurrentTab());
        }

        // 标签页按钮
        const tabButtons = DOMUtils.$$('.tab-btn');
        tabButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabType = e.target.dataset.type;
                this.switchTab(tabType);
            });
        });

        // ESC键关闭界面
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const leaderboardMenu = DOMUtils.$('#leaderboard-menu');
                if (leaderboardMenu && !DOMUtils.hasClass(leaderboardMenu, 'hidden')) {
                    this.hideLeaderboard();
                }
            }
        });
    }

    /**
     * 显示排行榜界面
     */
    async showLeaderboard() {
        const leaderboardMenu = DOMUtils.$('#leaderboard-menu');
        const mainMenu = DOMUtils.$('#main-menu');
        
        if (leaderboardMenu && mainMenu) {
            DOMUtils.removeClass(leaderboardMenu, 'hidden');
            DOMUtils.addClass(mainMenu, 'hidden');
            
            // 默认显示全球排行榜
            await this.switchTab('global');
        }
    }

    /**
     * 隐藏排行榜界面
     */
    hideLeaderboard() {
        const leaderboardMenu = DOMUtils.$('#leaderboard-menu');
        const mainMenu = DOMUtils.$('#main-menu');
        
        if (leaderboardMenu && mainMenu) {
            DOMUtils.addClass(leaderboardMenu, 'hidden');
            DOMUtils.removeClass(mainMenu, 'hidden');
        }
    }

    /**
     * 切换标签页
     */
    async switchTab(tabType) {
        // 更新标签页状态
        const tabButtons = DOMUtils.$$('.tab-btn');
        tabButtons.forEach(btn => {
            DOMUtils.removeClass(btn, 'active');
            if (btn.dataset.type === tabType) {
                DOMUtils.addClass(btn, 'active');
            }
        });

        this.currentTab = tabType;
        await this.updateLeaderboardDisplay();
    }

    /**
     * 刷新当前标签页
     */
    async refreshCurrentTab() {
        await this.updateLeaderboardDisplay();
        this.showNotification('排行榜已刷新', 'success');
    }

    /**
     * 更新排行榜显示
     */
    async updateLeaderboardDisplay() {
        try {
            let leaderboardData = [];
            let currentAccountRank = null;
            const currentAccount = await this.accountManager.getCurrentAccount();

            // 根据当前标签页获取数据
            switch (this.currentTab) {
                case 'global':
                    leaderboardData = await this.leaderboardManager.getLeaderboard('global');
                    if (currentAccount) {
                        currentAccountRank = await this.leaderboardManager.getAccountRank(
                            currentAccount.id, 'global'
                        );
                    }
                    break;
                case 'daily':
                    leaderboardData = await this.leaderboardManager.getLeaderboard('daily');
                    if (currentAccount) {
                        currentAccountRank = await this.leaderboardManager.getAccountRank(
                            currentAccount.id, 'daily'
                        );
                    }
                    break;
                case 'weekly':
                    leaderboardData = await this.leaderboardManager.getLeaderboard('weekly');
                    if (currentAccount) {
                        currentAccountRank = await this.leaderboardManager.getAccountRank(
                            currentAccount.id, 'weekly'
                        );
                    }
                    break;
                case 'personal':
                    if (currentAccount) {
                        leaderboardData = await this.leaderboardManager.getLeaderboard(
                            'account', currentAccount.id
                        );
                    }
                    break;
            }

            // 更新排行榜列表
            this.updateLeaderboardList(leaderboardData, currentAccount);
            
            // 更新当前排名信息
            this.updateCurrentRankInfo(currentAccountRank, currentAccount);

        } catch (error) {
            DebugUtils.log(`更新排行榜显示失败: ${error.message}`, 'error');
            this.showNotification('更新排行榜失败', 'error');
        }
    }

    /**
     * 更新排行榜列表
     */
    updateLeaderboardList(leaderboardData, currentAccount) {
        const leaderboardList = DOMUtils.$('#leaderboard-list');
        if (!leaderboardList) return;

        // 清空现有列表
        leaderboardList.innerHTML = '';

        if (leaderboardData.length === 0) {
            // 显示空状态
            const emptyDiv = document.createElement('div');
            emptyDiv.className = 'empty-leaderboard';
            emptyDiv.textContent = '暂无排行榜数据';
            leaderboardList.appendChild(emptyDiv);
            return;
        }

        // 生成排行榜项目
        leaderboardData.forEach((record, index) => {
            const item = this.createLeaderboardItem(record, index + 1, currentAccount);
            leaderboardList.appendChild(item);
        });
    }

    /**
     * 创建排行榜项目元素
     */
    createLeaderboardItem(record, rank, currentAccount) {
        const item = document.createElement('div');
        const isCurrentUser = currentAccount && record.accountId === currentAccount.id;
        
        item.className = `leaderboard-item ${isCurrentUser ? 'current-user' : ''}`;

        // 排名样式
        let rankClass = '';
        if (rank === 1) rankClass = 'top-1';
        else if (rank === 2) rankClass = 'top-2';
        else if (rank === 3) rankClass = 'top-3';

        // 格式化时间
        const playTimeMinutes = Math.floor(record.playTime / 60000);
        const recordDate = new Date(record.timestamp).toLocaleDateString();

        item.innerHTML = `
            <div class="rank-number ${rankClass}">${rank}</div>
            <div class="player-info">
                <div class="player-avatar">${record.accountName.charAt(0).toUpperCase()}</div>
                <div class="player-details">
                    <div class="player-name">${record.accountName}</div>
                    <div class="player-stats">
                        关卡 ${record.level} • 火花 ${record.sparksCollected} • ${playTimeMinutes}分钟
                    </div>
                </div>
            </div>
            <div class="score-info">
                <div class="score-value-large">${record.score.toLocaleString()}</div>
                <div class="score-details">${recordDate}</div>
            </div>
        `;

        return item;
    }

    /**
     * 更新当前排名信息
     */
    updateCurrentRankInfo(rankInfo, currentAccount) {
        const currentRankElement = DOMUtils.$('#current-rank');
        const bestScoreElement = DOMUtils.$('#best-score');

        if (currentRankElement) {
            if (rankInfo && rankInfo.rank > 0) {
                currentRankElement.textContent = `第 ${rankInfo.rank} 名`;
            } else {
                currentRankElement.textContent = '未上榜';
            }
        }

        if (bestScoreElement && currentAccount) {
            if (this.currentTab === 'personal' && rankInfo && rankInfo.record) {
                bestScoreElement.textContent = rankInfo.record.score.toLocaleString();
            } else {
                bestScoreElement.textContent = currentAccount.gameStats.bestScore.toLocaleString();
            }
        }
    }

    /**
     * 提交分数到排行榜
     * @param {object} scoreData - 分数数据
     * @returns {Promise<boolean>} 是否成功
     */
    async submitScore(scoreData) {
        try {
            const success = await this.leaderboardManager.submitScore(scoreData);
            
            if (success) {
                DebugUtils.log(`分数提交成功: ${scoreData.score}分`);
                
                // 如果排行榜界面正在显示，刷新显示
                const leaderboardMenu = DOMUtils.$('#leaderboard-menu');
                if (leaderboardMenu && !DOMUtils.hasClass(leaderboardMenu, 'hidden')) {
                    await this.updateLeaderboardDisplay();
                }
                
                return true;
            } else {
                DebugUtils.log('分数提交失败', 'error');
                return false;
            }
        } catch (error) {
            DebugUtils.log(`提交分数失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 这里可以实现通知显示逻辑
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // 简单的实现，后续可以改为更美观的通知组件
        if (type === 'error') {
            console.error(message);
        } else if (type === 'success') {
            console.log(message);
        }
    }

    /**
     * 获取当前账号的排行榜统计
     * @returns {Promise<object>} 统计信息
     */
    async getCurrentAccountStats() {
        try {
            const currentAccount = await this.accountManager.getCurrentAccount();
            if (!currentAccount) return null;

            const globalRank = await this.leaderboardManager.getAccountRank(
                currentAccount.id, 'global'
            );
            const dailyRank = await this.leaderboardManager.getAccountRank(
                currentAccount.id, 'daily'
            );
            const weeklyRank = await this.leaderboardManager.getAccountRank(
                currentAccount.id, 'weekly'
            );

            return {
                account: currentAccount,
                globalRank: globalRank,
                dailyRank: dailyRank,
                weeklyRank: weeklyRank
            };
        } catch (error) {
            DebugUtils.log(`获取账号统计失败: ${error.message}`, 'error');
            return null;
        }
    }
}

// 创建全局排行榜UI管理器实例
window.leaderboardUI = new LeaderboardUI();
