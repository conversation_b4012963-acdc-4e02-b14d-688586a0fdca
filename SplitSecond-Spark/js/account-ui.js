/**
 * 账号管理UI控制器 - SplitSecond Spark
 * 处理账号管理界面的交互逻辑
 */

class AccountUI {
    constructor() {
        this.accountManager = window.accountManager;
        this.selectedAvatar = '👤';
        
        this.initializeEventListeners();
        this.updateCurrentAccountDisplay();
        
        DebugUtils.log('账号管理UI初始化完成');
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 主菜单按钮
        const accountManagerBtn = DOMUtils.$('#account-manager-btn');
        if (accountManagerBtn) {
            accountManagerBtn.addEventListener('click', () => this.showAccountManager());
        }

        const switchAccountBtn = DOMUtils.$('#switch-account-btn');
        if (switchAccountBtn) {
            switchAccountBtn.addEventListener('click', () => this.showAccountManager());
        }

        // 账号管理界面按钮
        const accountManagerBackBtn = DOMUtils.$('#account-manager-back-btn');
        if (accountManagerBackBtn) {
            accountManagerBackBtn.addEventListener('click', () => this.hideAccountManager());
        }

        const createAccountBtn = DOMUtils.$('#create-account-btn');
        if (createAccountBtn) {
            createAccountBtn.addEventListener('click', () => this.showCreateAccountDialog());
        }

        const importAccountBtn = DOMUtils.$('#import-account-btn');
        if (importAccountBtn) {
            importAccountBtn.addEventListener('click', () => this.showImportAccountDialog());
        }

        const editAccountBtn = DOMUtils.$('#edit-account-btn');
        if (editAccountBtn) {
            editAccountBtn.addEventListener('click', () => this.editCurrentAccount());
        }

        const exportAccountBtn = DOMUtils.$('#export-account-btn');
        if (exportAccountBtn) {
            exportAccountBtn.addEventListener('click', () => this.exportCurrentAccount());
        }

        // 创建账号对话框
        const confirmCreateBtn = DOMUtils.$('#confirm-create-account');
        if (confirmCreateBtn) {
            confirmCreateBtn.addEventListener('click', () => this.createAccount());
        }

        const cancelCreateBtn = DOMUtils.$('#cancel-create-account');
        if (cancelCreateBtn) {
            cancelCreateBtn.addEventListener('click', () => this.hideCreateAccountDialog());
        }

        // 导入账号对话框
        const confirmImportBtn = DOMUtils.$('#confirm-import-account');
        if (confirmImportBtn) {
            confirmImportBtn.addEventListener('click', () => this.importAccount());
        }

        const cancelImportBtn = DOMUtils.$('#cancel-import-account');
        if (cancelImportBtn) {
            cancelImportBtn.addEventListener('click', () => this.hideImportAccountDialog());
        }

        // 头像选择器
        const avatarOptions = DOMUtils.$$('.avatar-option');
        avatarOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                // 移除其他选中状态
                avatarOptions.forEach(opt => DOMUtils.removeClass(opt, 'selected'));
                // 添加选中状态
                DOMUtils.addClass(e.target, 'selected');
                this.selectedAvatar = e.target.dataset.avatar;
            });
        });

        // ESC键关闭对话框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideAllDialogs();
            }
        });
    }

    /**
     * 显示账号管理界面
     */
    async showAccountManager() {
        const accountManagerMenu = DOMUtils.$('#account-manager-menu');
        const mainMenu = DOMUtils.$('#main-menu');
        
        if (accountManagerMenu && mainMenu) {
            DOMUtils.removeClass(accountManagerMenu, 'hidden');
            DOMUtils.addClass(mainMenu, 'hidden');
            
            await this.updateAccountManagerDisplay();
        }
    }

    /**
     * 隐藏账号管理界面
     */
    hideAccountManager() {
        const accountManagerMenu = DOMUtils.$('#account-manager-menu');
        const mainMenu = DOMUtils.$('#main-menu');
        
        if (accountManagerMenu && mainMenu) {
            DOMUtils.addClass(accountManagerMenu, 'hidden');
            DOMUtils.removeClass(mainMenu, 'hidden');
        }
    }

    /**
     * 显示创建账号对话框
     */
    showCreateAccountDialog() {
        const dialog = DOMUtils.$('#create-account-dialog');
        if (dialog) {
            DOMUtils.removeClass(dialog, 'hidden');
            
            // 清空输入框
            const nameInput = DOMUtils.$('#new-account-name');
            if (nameInput) {
                nameInput.value = '';
                nameInput.focus();
            }
            
            // 重置头像选择
            this.selectedAvatar = '👤';
            const avatarOptions = DOMUtils.$$('.avatar-option');
            avatarOptions.forEach(option => {
                DOMUtils.removeClass(option, 'selected');
                if (option.dataset.avatar === '👤') {
                    DOMUtils.addClass(option, 'selected');
                }
            });
        }
    }

    /**
     * 隐藏创建账号对话框
     */
    hideCreateAccountDialog() {
        const dialog = DOMUtils.$('#create-account-dialog');
        if (dialog) {
            DOMUtils.addClass(dialog, 'hidden');
        }
    }

    /**
     * 显示导入账号对话框
     */
    showImportAccountDialog() {
        const dialog = DOMUtils.$('#import-account-dialog');
        if (dialog) {
            DOMUtils.removeClass(dialog, 'hidden');
            
            // 清空文本区域
            const textarea = DOMUtils.$('#import-account-data');
            if (textarea) {
                textarea.value = '';
                textarea.focus();
            }
        }
    }

    /**
     * 隐藏导入账号对话框
     */
    hideImportAccountDialog() {
        const dialog = DOMUtils.$('#import-account-dialog');
        if (dialog) {
            DOMUtils.addClass(dialog, 'hidden');
        }
    }

    /**
     * 隐藏所有对话框
     */
    hideAllDialogs() {
        this.hideCreateAccountDialog();
        this.hideImportAccountDialog();
    }

    /**
     * 创建新账号
     */
    async createAccount() {
        const nameInput = DOMUtils.$('#new-account-name');
        if (!nameInput) return;

        const name = nameInput.value.trim();
        if (!name) {
            this.showNotification('请输入账号名称', 'error');
            return;
        }

        try {
            const account = await this.accountManager.createAccount(name, this.selectedAvatar);
            if (account) {
                this.showNotification(`账号 "${name}" 创建成功`, 'success');
                this.hideCreateAccountDialog();
                await this.updateAccountManagerDisplay();
            } else {
                this.showNotification('账号创建失败，名称可能已存在', 'error');
            }
        } catch (error) {
            DebugUtils.log(`创建账号失败: ${error.message}`, 'error');
            this.showNotification('账号创建失败', 'error');
        }
    }

    /**
     * 导入账号
     */
    async importAccount() {
        const textarea = DOMUtils.$('#import-account-data');
        if (!textarea) return;

        const importData = textarea.value.trim();
        if (!importData) {
            this.showNotification('请粘贴账号数据', 'error');
            return;
        }

        try {
            const accountId = await this.accountManager.importAccountData(importData);
            if (accountId) {
                this.showNotification('账号导入成功', 'success');
                this.hideImportAccountDialog();
                await this.updateAccountManagerDisplay();
            } else {
                this.showNotification('账号导入失败，数据格式可能无效', 'error');
            }
        } catch (error) {
            DebugUtils.log(`导入账号失败: ${error.message}`, 'error');
            this.showNotification('账号导入失败', 'error');
        }
    }

    /**
     * 编辑当前账号
     */
    async editCurrentAccount() {
        // 这里可以实现账号编辑功能
        this.showNotification('账号编辑功能开发中', 'info');
    }

    /**
     * 导出当前账号
     */
    async exportCurrentAccount() {
        try {
            const exportData = await this.accountManager.exportAccountData();
            if (exportData) {
                // 创建下载链接
                const blob = new Blob([exportData], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `account_backup_${Date.now()}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                this.showNotification('账号数据导出成功', 'success');
            } else {
                this.showNotification('账号数据导出失败', 'error');
            }
        } catch (error) {
            DebugUtils.log(`导出账号失败: ${error.message}`, 'error');
            this.showNotification('账号数据导出失败', 'error');
        }
    }

    /**
     * 切换到指定账号
     */
    async switchToAccount(accountId) {
        try {
            const success = await this.accountManager.switchAccount(accountId);
            if (success) {
                this.showNotification('账号切换成功', 'success');
                await this.updateCurrentAccountDisplay();
                await this.updateAccountManagerDisplay();
            } else {
                this.showNotification('账号切换失败', 'error');
            }
        } catch (error) {
            DebugUtils.log(`切换账号失败: ${error.message}`, 'error');
            this.showNotification('账号切换失败', 'error');
        }
    }

    /**
     * 删除指定账号
     */
    async deleteAccount(accountId) {
        const account = await this.accountManager.getAccount(accountId);
        if (!account) return;

        const confirmed = confirm(`确定要删除账号 "${account.name}" 吗？此操作不可撤销。`);
        if (!confirmed) return;

        try {
            const success = await this.accountManager.deleteAccount(accountId);
            if (success) {
                this.showNotification(`账号 "${account.name}" 删除成功`, 'success');
                await this.updateAccountManagerDisplay();
            } else {
                this.showNotification('账号删除失败', 'error');
            }
        } catch (error) {
            DebugUtils.log(`删除账号失败: ${error.message}`, 'error');
            this.showNotification('账号删除失败', 'error');
        }
    }

    /**
     * 更新当前账号显示
     */
    async updateCurrentAccountDisplay() {
        try {
            const currentAccount = await this.accountManager.getCurrentAccount();

            // 更新主菜单中的账号显示
            const currentAccountName = DOMUtils.$('#current-account-name');
            if (currentAccountName && currentAccount) {
                currentAccountName.textContent = currentAccount.name;
            }

            // 更新账号管理界面中的当前账号显示
            const currentAccountDisplayName = DOMUtils.$('#current-account-display-name');
            if (currentAccountDisplayName && currentAccount) {
                currentAccountDisplayName.textContent = currentAccount.name;
            }

            // 更新统计信息
            if (currentAccount) {
                const playtimeElement = DOMUtils.$('#current-account-playtime');
                const sparksElement = DOMUtils.$('#current-account-sparks');
                const scoreElement = DOMUtils.$('#current-account-score');

                if (playtimeElement) {
                    const minutes = Math.floor(currentAccount.gameStats.totalPlayTime / 60000);
                    playtimeElement.textContent = `${minutes}分钟`;
                }

                if (sparksElement) {
                    sparksElement.textContent = currentAccount.gameStats.totalSparks.toString();
                }

                if (scoreElement) {
                    scoreElement.textContent = currentAccount.gameStats.bestScore.toString();
                }
            }
        } catch (error) {
            DebugUtils.log(`更新当前账号显示失败: ${error.message}`, 'error');
        }
    }

    /**
     * 更新账号管理界面显示
     */
    async updateAccountManagerDisplay() {
        try {
            await this.updateCurrentAccountDisplay();
            await this.updateAccountsList();
        } catch (error) {
            DebugUtils.log(`更新账号管理界面失败: ${error.message}`, 'error');
        }
    }

    /**
     * 更新账号列表显示
     */
    async updateAccountsList() {
        try {
            const accounts = await this.accountManager.getAccountsList();
            const currentAccountId = this.accountManager.getCurrentAccountId();
            const accountsList = DOMUtils.$('#accounts-list');

            if (!accountsList) return;

            // 清空现有列表
            accountsList.innerHTML = '';

            // 生成账号卡片
            accounts.forEach(account => {
                const accountCard = this.createAccountCard(account, account.id === currentAccountId);
                accountsList.appendChild(accountCard);
            });
        } catch (error) {
            DebugUtils.log(`更新账号列表失败: ${error.message}`, 'error');
        }
    }

    /**
     * 创建账号卡片元素
     */
    createAccountCard(account, isCurrent = false) {
        const card = document.createElement('div');
        card.className = `account-card ${isCurrent ? 'current' : ''}`;

        const playtime = Math.floor(account.gameStats.totalPlayTime / 60000);
        const lastLogin = new Date(account.lastLoginAt).toLocaleDateString();

        card.innerHTML = `
            <div class="account-info">
                <span class="account-avatar-large">${account.avatar}</span>
                <div class="account-details">
                    <div class="account-name">${account.name}</div>
                    <div class="account-stats">
                        <span>游戏时间: ${playtime}分钟</span>
                        <span>收集火花: ${account.gameStats.totalSparks}</span>
                        <span>最高分数: ${account.gameStats.bestScore}</span>
                        <span>最后登录: ${lastLogin}</span>
                    </div>
                </div>
            </div>
            <div class="account-actions">
                ${!isCurrent ? `<button class="action-btn switch-btn" data-account-id="${account.id}">切换</button>` : ''}
                ${!isCurrent ? `<button class="action-btn delete-btn" data-account-id="${account.id}">删除</button>` : ''}
                <button class="action-btn export-btn" data-account-id="${account.id}">导出</button>
            </div>
        `;

        // 添加事件监听器
        const switchBtn = card.querySelector('.switch-btn');
        if (switchBtn) {
            switchBtn.addEventListener('click', () => {
                this.switchToAccount(account.id);
            });
        }

        const deleteBtn = card.querySelector('.delete-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => {
                this.deleteAccount(account.id);
            });
        }

        const exportBtn = card.querySelector('.export-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', async () => {
                await this.exportAccount(account.id);
            });
        }

        return card;
    }

    /**
     * 导出指定账号
     */
    async exportAccount(accountId) {
        try {
            const exportData = await this.accountManager.exportAccountData(accountId);
            if (exportData) {
                const account = await this.accountManager.getAccount(accountId);
                const filename = `${account.name}_backup_${Date.now()}.json`;

                // 创建下载链接
                const blob = new Blob([exportData], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.showNotification(`账号 "${account.name}" 数据导出成功`, 'success');
            } else {
                this.showNotification('账号数据导出失败', 'error');
            }
        } catch (error) {
            DebugUtils.log(`导出账号失败: ${error.message}`, 'error');
            this.showNotification('账号数据导出失败', 'error');
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 这里可以实现通知显示逻辑
        console.log(`[${type.toUpperCase()}] ${message}`);

        // 简单的alert实现，后续可以改为更美观的通知组件
        if (type === 'error') {
            alert(`错误: ${message}`);
        } else if (type === 'success') {
            alert(`成功: ${message}`);
        } else {
            alert(`信息: ${message}`);
        }
    }
}

// 创建全局账号UI管理器实例
window.accountUI = new AccountUI();
