/**
 * 关卡编辑器 - SplitSecond Spark
 * 可视化关卡编辑工具
 */

class LevelEditor {
    constructor() {
        this.levelManager = window.levelManager;
        this.currentLevel = null;
        this.isEditing = false;
        this.selectedTool = 'platform';
        this.selectedDimension = 'present';
        this.gridSize = 20;
        this.showGrid = true;
        
        // 编辑器状态
        this.editorState = {
            zoom: 1.0,
            offsetX: 0,
            offsetY: 0,
            isDragging: false,
            dragStart: { x: 0, y: 0 },
            selectedObject: null,
            clipboard: null
        };

        // 工具类型
        this.tools = {
            SELECT: 'select',
            PLATFORM: 'platform',
            SPARK: 'spark',
            OBSTACLE: 'obstacle',
            PORTAL: 'portal',
            TRIGGER: 'trigger',
            PLAYER_START: 'playerStart',
            ERASER: 'eraser'
        };

        // 对象类型配置
        this.objectTypes = {
            platform: {
                name: '平台',
                color: '#8B4513',
                defaultSize: { width: 100, height: 20 }
            },
            spark: {
                name: '能量火花',
                color: '#FFD700',
                defaultSize: { width: 16, height: 16 }
            },
            obstacle: {
                name: '障碍物',
                color: '#FF4500',
                defaultSize: { width: 40, height: 40 }
            },
            portal: {
                name: '传送门',
                color: '#9932CC',
                defaultSize: { width: 60, height: 80 }
            },
            trigger: {
                name: '触发器',
                color: '#00CED1',
                defaultSize: { width: 50, height: 50 }
            }
        };

        this.initializeEditor();
        
        DebugUtils.log('关卡编辑器初始化完成');
    }

    /**
     * 初始化编辑器
     */
    initializeEditor() {
        this.setupCanvas();
        this.setupEventListeners();
    }

    /**
     * 设置画布
     */
    setupCanvas() {
        this.canvas = DOMUtils.$('#level-editor-canvas');
        if (!this.canvas) {
            DebugUtils.log('找不到关卡编辑器画布', 'error');
            return;
        }

        this.ctx = this.canvas.getContext('2d');
        this.resizeCanvas();
        
        // 设置画布样式
        this.canvas.style.cursor = 'crosshair';
        this.canvas.style.border = '2px solid #333';
        this.canvas.style.background = '#1a1a1a';
    }

    /**
     * 调整画布大小
     */
    resizeCanvas() {
        if (!this.canvas) return;
        
        const container = this.canvas.parentElement;
        if (container) {
            this.canvas.width = container.clientWidth;
            this.canvas.height = container.clientHeight;
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        if (!this.canvas) return;

        // 鼠标事件
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        this.canvas.addEventListener('wheel', (e) => this.handleWheel(e));

        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));

        // 窗口大小变化
        window.addEventListener('resize', () => this.resizeCanvas());
    }

    /**
     * 创建新关卡
     * @param {string} name - 关卡名称
     * @param {string} description - 关卡描述
     */
    async createNewLevel(name, description = '') {
        try {
            const level = await this.levelManager.createLevel(name, description);
            if (level) {
                this.currentLevel = level;
                this.isEditing = true;
                this.resetEditorState();
                this.render();
                DebugUtils.log(`开始编辑新关卡: ${name}`);
                return true;
            }
            return false;
        } catch (error) {
            DebugUtils.log(`创建新关卡失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 加载关卡进行编辑
     * @param {string} levelId - 关卡ID
     */
    async loadLevel(levelId) {
        try {
            const level = await this.levelManager.loadLevel(levelId);
            if (level) {
                this.currentLevel = level;
                this.isEditing = true;
                this.resetEditorState();
                this.render();
                DebugUtils.log(`开始编辑关卡: ${level.name}`);
                return true;
            }
            return false;
        } catch (error) {
            DebugUtils.log(`加载关卡失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 保存当前关卡
     */
    async saveLevel() {
        if (!this.currentLevel) {
            DebugUtils.log('没有当前关卡可保存', 'error');
            return false;
        }

        try {
            const success = await this.levelManager.saveLevel(this.currentLevel);
            if (success) {
                DebugUtils.log(`关卡保存成功: ${this.currentLevel.name}`);
            }
            return success;
        } catch (error) {
            DebugUtils.log(`保存关卡失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 重置编辑器状态
     */
    resetEditorState() {
        this.editorState = {
            zoom: 1.0,
            offsetX: 0,
            offsetY: 0,
            isDragging: false,
            dragStart: { x: 0, y: 0 },
            selectedObject: null,
            clipboard: null
        };
    }

    /**
     * 设置当前工具
     * @param {string} tool - 工具类型
     */
    setTool(tool) {
        if (this.tools[tool.toUpperCase()]) {
            this.selectedTool = tool;
            this.updateCursor();
            DebugUtils.log(`切换到工具: ${tool}`);
        }
    }

    /**
     * 设置当前时间维度
     * @param {string} dimension - 时间维度
     */
    setDimension(dimension) {
        if (['past', 'present', 'future'].includes(dimension)) {
            this.selectedDimension = dimension;
            this.render();
            DebugUtils.log(`切换到时间维度: ${dimension}`);
        }
    }

    /**
     * 更新鼠标光标
     */
    updateCursor() {
        if (!this.canvas) return;
        
        switch (this.selectedTool) {
            case this.tools.SELECT:
                this.canvas.style.cursor = 'default';
                break;
            case this.tools.ERASER:
                this.canvas.style.cursor = 'crosshair';
                break;
            default:
                this.canvas.style.cursor = 'crosshair';
                break;
        }
    }

    /**
     * 处理鼠标按下事件
     */
    handleMouseDown(e) {
        if (!this.isEditing) return;

        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // 转换为世界坐标
        const worldPos = this.screenToWorld(x, y);
        
        this.editorState.isDragging = true;
        this.editorState.dragStart = { x: worldPos.x, y: worldPos.y };

        switch (this.selectedTool) {
            case this.tools.SELECT:
                this.handleSelectTool(worldPos);
                break;
            case this.tools.PLATFORM:
                this.handlePlatformTool(worldPos);
                break;
            case this.tools.SPARK:
                this.handleSparkTool(worldPos);
                break;
            case this.tools.OBSTACLE:
                this.handleObstacleTool(worldPos);
                break;
            case this.tools.PORTAL:
                this.handlePortalTool(worldPos);
                break;
            case this.tools.TRIGGER:
                this.handleTriggerTool(worldPos);
                break;
            case this.tools.PLAYER_START:
                this.handlePlayerStartTool(worldPos);
                break;
            case this.tools.ERASER:
                this.handleEraserTool(worldPos);
                break;
        }

        this.render();
    }

    /**
     * 处理鼠标移动事件
     */
    handleMouseMove(e) {
        if (!this.isEditing) return;

        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const worldPos = this.screenToWorld(x, y);

        if (this.editorState.isDragging) {
            // 处理拖拽操作
            if (this.selectedTool === this.tools.SELECT && this.editorState.selectedObject) {
                this.moveSelectedObject(worldPos);
                this.render();
            }
        }
    }

    /**
     * 处理鼠标释放事件
     */
    handleMouseUp(e) {
        this.editorState.isDragging = false;
    }

    /**
     * 处理滚轮事件（缩放）
     */
    handleWheel(e) {
        e.preventDefault();
        
        const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
        const newZoom = Math.max(0.1, Math.min(5.0, this.editorState.zoom * zoomFactor));
        
        if (newZoom !== this.editorState.zoom) {
            this.editorState.zoom = newZoom;
            this.render();
        }
    }

    /**
     * 处理键盘按下事件
     */
    handleKeyDown(e) {
        if (!this.isEditing) return;

        switch (e.key) {
            case 'Delete':
                this.deleteSelectedObject();
                break;
            case 'c':
                if (e.ctrlKey) {
                    this.copySelectedObject();
                }
                break;
            case 'v':
                if (e.ctrlKey) {
                    this.pasteObject();
                }
                break;
            case 's':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.saveLevel();
                }
                break;
            case 'g':
                this.showGrid = !this.showGrid;
                this.render();
                break;
        }
    }

    /**
     * 处理键盘释放事件
     */
    handleKeyUp(e) {
        // 预留给需要的键盘释放处理
    }

    /**
     * 屏幕坐标转世界坐标
     */
    screenToWorld(screenX, screenY) {
        return {
            x: (screenX - this.editorState.offsetX) / this.editorState.zoom,
            y: (screenY - this.editorState.offsetY) / this.editorState.zoom
        };
    }

    /**
     * 世界坐标转屏幕坐标
     */
    worldToScreen(worldX, worldY) {
        return {
            x: worldX * this.editorState.zoom + this.editorState.offsetX,
            y: worldY * this.editorState.zoom + this.editorState.offsetY
        };
    }

    /**
     * 对齐到网格
     */
    snapToGrid(x, y) {
        if (!this.showGrid) return { x, y };

        return {
            x: Math.round(x / this.gridSize) * this.gridSize,
            y: Math.round(y / this.gridSize) * this.gridSize
        };
    }

    /**
     * 处理选择工具
     */
    handleSelectTool(worldPos) {
        const selectedObj = this.findObjectAt(worldPos);
        this.editorState.selectedObject = selectedObj;

        if (selectedObj) {
            DebugUtils.log(`选中对象: ${selectedObj.type} at (${selectedObj.x}, ${selectedObj.y})`);
        }
    }

    /**
     * 处理平台工具
     */
    handlePlatformTool(worldPos) {
        const snappedPos = this.snapToGrid(worldPos.x, worldPos.y);
        const platform = {
            type: 'platform',
            x: snappedPos.x,
            y: snappedPos.y,
            width: this.objectTypes.platform.defaultSize.width,
            height: this.objectTypes.platform.defaultSize.height,
            dimension: this.selectedDimension,
            id: this.generateObjectId()
        };

        this.addObjectToLevel(platform);
        DebugUtils.log(`添加平台: (${platform.x}, ${platform.y})`);
    }

    /**
     * 处理火花工具
     */
    handleSparkTool(worldPos) {
        const snappedPos = this.snapToGrid(worldPos.x, worldPos.y);
        const spark = {
            type: 'spark',
            x: snappedPos.x,
            y: snappedPos.y,
            width: this.objectTypes.spark.defaultSize.width,
            height: this.objectTypes.spark.defaultSize.height,
            dimension: this.selectedDimension,
            value: 10, // 默认分值
            id: this.generateObjectId()
        };

        this.addObjectToLevel(spark);
        DebugUtils.log(`添加火花: (${spark.x}, ${spark.y})`);
    }

    /**
     * 处理障碍物工具
     */
    handleObstacleTool(worldPos) {
        const snappedPos = this.snapToGrid(worldPos.x, worldPos.y);
        const obstacle = {
            type: 'obstacle',
            x: snappedPos.x,
            y: snappedPos.y,
            width: this.objectTypes.obstacle.defaultSize.width,
            height: this.objectTypes.obstacle.defaultSize.height,
            dimension: this.selectedDimension,
            damage: 1, // 默认伤害
            id: this.generateObjectId()
        };

        this.addObjectToLevel(obstacle);
        DebugUtils.log(`添加障碍物: (${obstacle.x}, ${obstacle.y})`);
    }

    /**
     * 处理传送门工具
     */
    handlePortalTool(worldPos) {
        const snappedPos = this.snapToGrid(worldPos.x, worldPos.y);
        const portal = {
            type: 'portal',
            x: snappedPos.x,
            y: snappedPos.y,
            width: this.objectTypes.portal.defaultSize.width,
            height: this.objectTypes.portal.defaultSize.height,
            dimension: this.selectedDimension,
            targetX: snappedPos.x + 100,
            targetY: snappedPos.y,
            id: this.generateObjectId()
        };

        this.addObjectToLevel(portal);
        DebugUtils.log(`添加传送门: (${portal.x}, ${portal.y})`);
    }

    /**
     * 处理触发器工具
     */
    handleTriggerTool(worldPos) {
        const snappedPos = this.snapToGrid(worldPos.x, worldPos.y);
        const trigger = {
            type: 'trigger',
            x: snappedPos.x,
            y: snappedPos.y,
            width: this.objectTypes.trigger.defaultSize.width,
            height: this.objectTypes.trigger.defaultSize.height,
            dimension: this.selectedDimension,
            action: 'switch_dimension', // 默认动作
            id: this.generateObjectId()
        };

        this.addObjectToLevel(trigger);
        DebugUtils.log(`添加触发器: (${trigger.x}, ${trigger.y})`);
    }

    /**
     * 处理玩家起始点工具
     */
    handlePlayerStartTool(worldPos) {
        const snappedPos = this.snapToGrid(worldPos.x, worldPos.y);

        if (this.currentLevel) {
            this.currentLevel.data.playerStart = {
                x: snappedPos.x,
                y: snappedPos.y
            };
            DebugUtils.log(`设置玩家起始点: (${snappedPos.x}, ${snappedPos.y})`);
        }
    }

    /**
     * 处理橡皮擦工具
     */
    handleEraserTool(worldPos) {
        const objectToDelete = this.findObjectAt(worldPos);
        if (objectToDelete) {
            this.removeObjectFromLevel(objectToDelete);
            DebugUtils.log(`删除对象: ${objectToDelete.type}`);
        }
    }

    /**
     * 在指定位置查找对象
     */
    findObjectAt(worldPos) {
        if (!this.currentLevel) return null;

        const objects = this.getAllObjectsInDimension(this.selectedDimension);

        // 从后往前查找（后添加的对象优先）
        for (let i = objects.length - 1; i >= 0; i--) {
            const obj = objects[i];
            if (this.isPointInObject(worldPos, obj)) {
                return obj;
            }
        }

        return null;
    }

    /**
     * 检查点是否在对象内
     */
    isPointInObject(point, obj) {
        return point.x >= obj.x &&
               point.x <= obj.x + obj.width &&
               point.y >= obj.y &&
               point.y <= obj.y + obj.height;
    }

    /**
     * 获取指定维度的所有对象
     */
    getAllObjectsInDimension(dimension) {
        if (!this.currentLevel) return [];

        const objects = [];
        const data = this.currentLevel.data;

        // 收集所有类型的对象
        ['platforms', 'sparks', 'obstacles', 'portals', 'triggers'].forEach(type => {
            if (data[type]) {
                objects.push(...data[type].filter(obj => obj.dimension === dimension));
            }
        });

        return objects;
    }

    /**
     * 添加对象到关卡
     */
    addObjectToLevel(obj) {
        if (!this.currentLevel) return;

        const data = this.currentLevel.data;
        const arrayName = obj.type + 's'; // platform -> platforms

        if (!data[arrayName]) {
            data[arrayName] = [];
        }

        data[arrayName].push(obj);
    }

    /**
     * 从关卡中移除对象
     */
    removeObjectFromLevel(obj) {
        if (!this.currentLevel) return;

        const data = this.currentLevel.data;
        const arrayName = obj.type + 's';

        if (data[arrayName]) {
            const index = data[arrayName].findIndex(item => item.id === obj.id);
            if (index !== -1) {
                data[arrayName].splice(index, 1);
            }
        }
    }

    /**
     * 生成对象ID
     */
    generateObjectId() {
        return 'obj_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 移动选中的对象
     */
    moveSelectedObject(worldPos) {
        if (!this.editorState.selectedObject) return;

        const snappedPos = this.snapToGrid(worldPos.x, worldPos.y);
        this.editorState.selectedObject.x = snappedPos.x;
        this.editorState.selectedObject.y = snappedPos.y;
    }

    /**
     * 删除选中的对象
     */
    deleteSelectedObject() {
        if (this.editorState.selectedObject) {
            this.removeObjectFromLevel(this.editorState.selectedObject);
            this.editorState.selectedObject = null;
            this.render();
            DebugUtils.log('删除选中对象');
        }
    }

    /**
     * 复制选中的对象
     */
    copySelectedObject() {
        if (this.editorState.selectedObject) {
            this.editorState.clipboard = JSON.parse(JSON.stringify(this.editorState.selectedObject));
            DebugUtils.log('复制对象到剪贴板');
        }
    }

    /**
     * 粘贴对象
     */
    pasteObject() {
        if (this.editorState.clipboard) {
            const newObj = JSON.parse(JSON.stringify(this.editorState.clipboard));
            newObj.id = this.generateObjectId();
            newObj.x += 20; // 偏移一点位置
            newObj.y += 20;

            this.addObjectToLevel(newObj);
            this.editorState.selectedObject = newObj;
            this.render();
            DebugUtils.log('粘贴对象');
        }
    }

    /**
     * 渲染编辑器
     */
    render() {
        if (!this.ctx || !this.canvas) return;

        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 保存上下文状态
        this.ctx.save();

        // 应用变换
        this.ctx.translate(this.editorState.offsetX, this.editorState.offsetY);
        this.ctx.scale(this.editorState.zoom, this.editorState.zoom);

        // 绘制网格
        if (this.showGrid) {
            this.renderGrid();
        }

        // 绘制关卡内容
        if (this.currentLevel) {
            this.renderLevel();
        }

        // 恢复上下文状态
        this.ctx.restore();

        // 绘制UI元素（不受变换影响）
        this.renderUI();
    }

    /**
     * 渲染网格
     */
    renderGrid() {
        const ctx = this.ctx;
        const gridSize = this.gridSize;
        const canvasWidth = this.canvas.width / this.editorState.zoom;
        const canvasHeight = this.canvas.height / this.editorState.zoom;

        ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.lineWidth = 1 / this.editorState.zoom;

        // 垂直线
        for (let x = 0; x < canvasWidth; x += gridSize) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, canvasHeight);
            ctx.stroke();
        }

        // 水平线
        for (let y = 0; y < canvasHeight; y += gridSize) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(canvasWidth, y);
            ctx.stroke();
        }
    }

    /**
     * 渲染关卡
     */
    renderLevel() {
        if (!this.currentLevel) return;

        const data = this.currentLevel.data;

        // 渲染玩家起始点
        this.renderPlayerStart(data.playerStart);

        // 渲染当前维度的对象
        this.renderDimensionObjects(this.selectedDimension);

        // 渲染选中对象的高亮
        if (this.editorState.selectedObject) {
            this.renderSelection(this.editorState.selectedObject);
        }
    }

    /**
     * 渲染玩家起始点
     */
    renderPlayerStart(playerStart) {
        if (!playerStart) return;

        const ctx = this.ctx;
        ctx.fillStyle = '#00FF00';
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 2 / this.editorState.zoom;

        // 绘制圆形标记
        ctx.beginPath();
        ctx.arc(playerStart.x + 16, playerStart.y + 16, 16, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();

        // 绘制文字
        ctx.fillStyle = '#FFFFFF';
        ctx.font = `${12 / this.editorState.zoom}px Arial`;
        ctx.textAlign = 'center';
        ctx.fillText('START', playerStart.x + 16, playerStart.y + 20);
    }

    /**
     * 渲染指定维度的对象
     */
    renderDimensionObjects(dimension) {
        if (!this.currentLevel) return;

        const data = this.currentLevel.data;
        const dimColor = data.dimensions[dimension]?.color || '#FFFFFF';

        // 渲染平台
        if (data.platforms) {
            data.platforms
                .filter(platform => platform.dimension === dimension)
                .forEach(platform => this.renderPlatform(platform, dimColor));
        }

        // 渲染火花
        if (data.sparks) {
            data.sparks
                .filter(spark => spark.dimension === dimension)
                .forEach(spark => this.renderSpark(spark));
        }

        // 渲染障碍物
        if (data.obstacles) {
            data.obstacles
                .filter(obstacle => obstacle.dimension === dimension)
                .forEach(obstacle => this.renderObstacle(obstacle, dimColor));
        }

        // 渲染传送门
        if (data.portals) {
            data.portals
                .filter(portal => portal.dimension === dimension)
                .forEach(portal => this.renderPortal(portal, dimColor));
        }

        // 渲染触发器
        if (data.triggers) {
            data.triggers
                .filter(trigger => trigger.dimension === dimension)
                .forEach(trigger => this.renderTrigger(trigger, dimColor));
        }
    }

    /**
     * 渲染平台
     */
    renderPlatform(platform, dimColor) {
        const ctx = this.ctx;
        ctx.fillStyle = dimColor;
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 1 / this.editorState.zoom;

        ctx.fillRect(platform.x, platform.y, platform.width, platform.height);
        ctx.strokeRect(platform.x, platform.y, platform.width, platform.height);
    }

    /**
     * 渲染火花
     */
    renderSpark(spark) {
        const ctx = this.ctx;
        ctx.fillStyle = '#FFD700';
        ctx.strokeStyle = '#FFA500';
        ctx.lineWidth = 1 / this.editorState.zoom;

        // 绘制星形
        const centerX = spark.x + spark.width / 2;
        const centerY = spark.y + spark.height / 2;
        const radius = Math.min(spark.width, spark.height) / 2;

        ctx.beginPath();
        for (let i = 0; i < 5; i++) {
            const angle = (i * 4 * Math.PI) / 5;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;
            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
    }

    /**
     * 渲染障碍物
     */
    renderObstacle(obstacle, dimColor) {
        const ctx = this.ctx;
        ctx.fillStyle = '#FF4500';
        ctx.strokeStyle = dimColor;
        ctx.lineWidth = 2 / this.editorState.zoom;

        ctx.fillRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
        ctx.strokeRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);

        // 绘制危险标记
        ctx.fillStyle = '#FFFFFF';
        ctx.font = `${16 / this.editorState.zoom}px Arial`;
        ctx.textAlign = 'center';
        ctx.fillText('!', obstacle.x + obstacle.width / 2, obstacle.y + obstacle.height / 2 + 6);
    }

    /**
     * 渲染传送门
     */
    renderPortal(portal, dimColor) {
        const ctx = this.ctx;
        ctx.fillStyle = '#9932CC';
        ctx.strokeStyle = dimColor;
        ctx.lineWidth = 2 / this.editorState.zoom;

        // 绘制椭圆形传送门
        ctx.beginPath();
        ctx.ellipse(
            portal.x + portal.width / 2,
            portal.y + portal.height / 2,
            portal.width / 2,
            portal.height / 2,
            0, 0, Math.PI * 2
        );
        ctx.fill();
        ctx.stroke();

        // 绘制目标连线
        if (portal.targetX !== undefined && portal.targetY !== undefined) {
            ctx.strokeStyle = 'rgba(153, 50, 204, 0.5)';
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(portal.x + portal.width / 2, portal.y + portal.height / 2);
            ctx.lineTo(portal.targetX, portal.targetY);
            ctx.stroke();
            ctx.setLineDash([]);
        }
    }

    /**
     * 渲染触发器
     */
    renderTrigger(trigger, dimColor) {
        const ctx = this.ctx;
        ctx.fillStyle = 'rgba(0, 206, 209, 0.5)';
        ctx.strokeStyle = dimColor;
        ctx.lineWidth = 2 / this.editorState.zoom;

        ctx.fillRect(trigger.x, trigger.y, trigger.width, trigger.height);
        ctx.strokeRect(trigger.x, trigger.y, trigger.width, trigger.height);

        // 绘制触发器标记
        ctx.fillStyle = '#FFFFFF';
        ctx.font = `${12 / this.editorState.zoom}px Arial`;
        ctx.textAlign = 'center';
        ctx.fillText('T', trigger.x + trigger.width / 2, trigger.y + trigger.height / 2 + 4);
    }

    /**
     * 渲染选中对象的高亮
     */
    renderSelection(obj) {
        const ctx = this.ctx;
        ctx.strokeStyle = '#00FF00';
        ctx.lineWidth = 3 / this.editorState.zoom;
        ctx.setLineDash([5, 5]);

        ctx.strokeRect(obj.x - 2, obj.y - 2, obj.width + 4, obj.height + 4);
        ctx.setLineDash([]);
    }

    /**
     * 渲染UI元素
     */
    renderUI() {
        const ctx = this.ctx;

        // 渲染工具信息
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(10, 10, 200, 80);

        ctx.fillStyle = '#FFFFFF';
        ctx.font = '14px Arial';
        ctx.textAlign = 'left';
        ctx.fillText(`工具: ${this.selectedTool}`, 20, 30);
        ctx.fillText(`维度: ${this.selectedDimension}`, 20, 50);
        ctx.fillText(`缩放: ${(this.editorState.zoom * 100).toFixed(0)}%`, 20, 70);

        // 渲染帮助信息
        if (this.currentLevel) {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(10, this.canvas.height - 60, 300, 50);

            ctx.fillStyle = '#FFFFFF';
            ctx.font = '12px Arial';
            ctx.fillText('快捷键: G-网格 Del-删除 Ctrl+C-复制 Ctrl+V-粘贴 Ctrl+S-保存', 20, this.canvas.height - 40);
            ctx.fillText('鼠标滚轮缩放，拖拽移动对象', 20, this.canvas.height - 20);
        }
    }
}

// 创建全局关卡编辑器实例
window.levelEditor = new LevelEditor();
