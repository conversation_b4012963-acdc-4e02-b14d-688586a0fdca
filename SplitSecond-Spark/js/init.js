/**
 * 游戏初始化脚本 - SplitSecond Spark
 * 确保所有系统按正确顺序初始化
 */

(function() {
    'use strict';
    
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeGame);
    } else {
        initializeGame();
    }
    
    /**
     * 初始化游戏
     */
    function initializeGame() {
        console.log('开始初始化 SplitSecond Spark...');
        
        try {
            // 检查必要的依赖
            checkDependencies();
            
            // 初始化全局系统
            initializeGlobalSystems();
            
            // 设置错误处理
            setupErrorHandling();
            
            // 显示加载完成
            hideLoadingScreen();
            
            console.log('SplitSecond Spark 初始化完成！');
            
        } catch (error) {
            console.error('游戏初始化失败:', error);
            showInitializationError(error);
        }
    }
    
    /**
     * 检查依赖
     */
    function checkDependencies() {
        const requiredClasses = [
            'MathUtils', 'ColorUtils', 'DOMUtils', 'DebugUtils', 'TimeUtils',
            'KVStorageService', 'AccountManager', 'AccountUI',
            'LeaderboardManager', 'LeaderboardUI',
            'LevelManager', 'LevelEditor', 'LevelManagerUI', 'LevelEditorUI',
            'StorageManager', 'InputManager', 'Renderer',
            'Particle', 'ParticleSystem',
            'Entity', 'Obstacle', 'Portal', 'Collectible',
            'GameWorld', 'TimeDimension', 'Player',
            'EnergySpark', 'SparkSystem', 'Game'
        ];

        const missing = [];

        requiredClasses.forEach(className => {
            if (!window[className]) {
                missing.push(className);
            }
        });

        if (missing.length > 0) {
            throw new Error(`缺少必要的类: ${missing.join(', ')}`);
        }

        console.log('所有依赖检查通过');
    }
    
    /**
     * 初始化全局系统
     */
    function initializeGlobalSystems() {
        // 初始化KV存储服务
        if (!window.kvStorage) {
            window.kvStorage = new KVStorageService();
            console.log('KV存储服务初始化完成');
        }

        // 初始化账号管理器
        if (!window.accountManager) {
            window.accountManager = new AccountManager();
            console.log('账号管理器初始化完成');
        }

        // 初始化排行榜管理器
        if (!window.leaderboardManager) {
            window.leaderboardManager = new LeaderboardManager();
            console.log('排行榜管理器初始化完成');
        }

        // 初始化关卡管理器
        if (!window.levelManager) {
            window.levelManager = new LevelManager();
            console.log('关卡管理器初始化完成');
        }

        // 初始化存储管理器（向后兼容）
        if (!window.storageManager) {
            window.storageManager = new StorageManager();
            console.log('存储管理器初始化完成');
        }

        // 初始化输入管理器
        if (!window.inputManager) {
            window.inputManager = new InputManager();
            console.log('输入管理器初始化完成');
        }

        // 初始化渲染器
        if (!window.renderer) {
            const canvas = document.getElementById('game-canvas');
            if (!canvas) {
                throw new Error('找不到游戏画布元素');
            }

            window.renderer = new Renderer(canvas);
            console.log('渲染器初始化完成');
        }

        // 初始化粒子系统
        if (!window.particleSystem) {
            window.particleSystem = new ParticleSystem();
            console.log('粒子系统初始化完成');
        }

        // 初始化时间维度系统
        if (!window.timeDimension) {
            window.timeDimension = new TimeDimension();
            console.log('时间维度系统初始化完成');
        }

        console.log('所有全局系统初始化完成');
    }
    
    /**
     * 设置错误处理
     */
    function setupErrorHandling() {
        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error);
            showError('发生了一个错误: ' + event.error.message);
        });
        
        // Promise 错误处理
        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的 Promise 错误:', event.reason);
            showError('异步操作失败: ' + event.reason);
        });
    }
    
    /**
     * 隐藏加载屏幕
     */
    function hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            setTimeout(() => {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }, 1000); // 显示加载屏幕至少1秒
        }
    }
    
    /**
     * 显示初始化错误
     * @param {Error} error - 错误对象
     */
    function showInitializationError(error) {
        const errorHtml = `
            <div style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                font-family: 'Arial', sans-serif;
            ">
                <div style="
                    background: rgba(255, 255, 255, 0.1);
                    backdrop-filter: blur(10px);
                    border-radius: 20px;
                    padding: 40px;
                    text-align: center;
                    color: white;
                    max-width: 500px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                ">
                    <h1 style="margin: 0 0 20px 0; font-size: 2em;">⚠️ 初始化失败</h1>
                    <p style="margin: 0 0 20px 0; font-size: 1.1em; line-height: 1.5;">
                        很抱歉，游戏无法正常启动。
                    </p>
                    <div style="
                        background: rgba(255, 0, 0, 0.2);
                        border: 1px solid rgba(255, 0, 0, 0.3);
                        border-radius: 10px;
                        padding: 15px;
                        margin: 20px 0;
                        font-family: monospace;
                        font-size: 0.9em;
                        text-align: left;
                    ">
                        ${error.message}
                    </div>
                    <button onclick="location.reload()" style="
                        background: linear-gradient(45deg, #ff6b6b, #ee5a24);
                        border: none;
                        color: white;
                        padding: 12px 24px;
                        border-radius: 25px;
                        cursor: pointer;
                        font-size: 1em;
                        font-weight: bold;
                        transition: transform 0.2s;
                    " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                        重新加载游戏
                    </button>
                </div>
            </div>
        `;
        
        document.body.innerHTML = errorHtml;
    }
    
    /**
     * 显示运行时错误
     * @param {string} message - 错误消息
     */
    function showError(message) {
        // 创建错误提示元素
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 0, 0, 0.9);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            animation: slideIn 0.3s ease-out;
        `;
        
        errorDiv.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 18px;">⚠️</span>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: none;
                    border: none;
                    color: white;
                    font-size: 16px;
                    cursor: pointer;
                    margin-left: auto;
                ">×</button>
            </div>
        `;
        
        // 添加动画样式
        if (!document.getElementById('error-animations')) {
            const style = document.createElement('style');
            style.id = 'error-animations';
            style.textContent = `
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(errorDiv);
        
        // 5秒后自动移除
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 5000);
    }
    
    // 导出错误显示函数供其他模块使用
    window.showError = showError;
    
})();
