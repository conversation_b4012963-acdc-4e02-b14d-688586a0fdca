/**
 * 关卡管理器UI控制器 - SplitSecond Spark
 * 处理关卡管理界面的交互逻辑
 */

class LevelManagerUI {
    constructor() {
        this.levelManager = window.levelManager;
        this.levelEditor = window.levelEditor;
        this.currentTab = 'my';
        this.currentLevels = [];
        
        this.initializeEventListeners();
        
        DebugUtils.log('关卡管理器UI初始化完成');
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 主菜单按钮
        const levelEditorBtn = DOMUtils.$('#level-editor-btn');
        if (levelEditorBtn) {
            levelEditorBtn.addEventListener('click', () => this.showLevelManager());
        }

        // 关卡管理界面按钮
        const levelManagerBackBtn = DOMUtils.$('#level-manager-back-btn');
        if (levelManagerBackBtn) {
            levelManagerBackBtn.addEventListener('click', () => this.hideLevelManager());
        }

        const createLevelBtn = DOMUtils.$('#create-level-btn');
        if (createLevelBtn) {
            createLevelBtn.addEventListener('click', () => this.showCreateLevelDialog());
        }

        const importLevelBtn = DOMUtils.$('#import-level-btn');
        if (importLevelBtn) {
            importLevelBtn.addEventListener('click', () => this.importLevel());
        }

        // 标签页按钮
        const tabButtons = DOMUtils.$$('.level-manager-tabs .tab-btn');
        tabButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabType = e.target.dataset.type;
                this.switchTab(tabType);
            });
        });

        // 搜索和筛选
        const searchInput = DOMUtils.$('#level-search');
        if (searchInput) {
            searchInput.addEventListener('input', () => this.filterLevels());
        }

        const difficultyFilter = DOMUtils.$('#difficulty-filter');
        if (difficultyFilter) {
            difficultyFilter.addEventListener('change', () => this.filterLevels());
        }

        // 创建关卡对话框
        const confirmCreateBtn = DOMUtils.$('#confirm-create-level-btn');
        if (confirmCreateBtn) {
            confirmCreateBtn.addEventListener('click', () => this.createNewLevel());
        }

        const cancelCreateBtn = DOMUtils.$('#cancel-create-level-btn');
        if (cancelCreateBtn) {
            cancelCreateBtn.addEventListener('click', () => this.hideCreateLevelDialog());
        }

        // 关卡设置对话框
        const saveSettingsBtn = DOMUtils.$('#save-settings-btn');
        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', () => this.saveLevelSettings());
        }

        const cancelSettingsBtn = DOMUtils.$('#cancel-settings-btn');
        if (cancelSettingsBtn) {
            cancelSettingsBtn.addEventListener('click', () => this.hideLevelSettingsDialog());
        }

        // ESC键关闭界面
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const levelManagerMenu = DOMUtils.$('#level-manager-menu');
                if (levelManagerMenu && !DOMUtils.hasClass(levelManagerMenu, 'hidden')) {
                    this.hideLevelManager();
                }
            }
        });
    }

    /**
     * 显示关卡管理界面
     */
    async showLevelManager() {
        const levelManagerMenu = DOMUtils.$('#level-manager-menu');
        const mainMenu = DOMUtils.$('#main-menu');
        
        if (levelManagerMenu && mainMenu) {
            DOMUtils.removeClass(levelManagerMenu, 'hidden');
            DOMUtils.addClass(mainMenu, 'hidden');
            
            // 默认显示我的关卡
            await this.switchTab('my');
        }
    }

    /**
     * 隐藏关卡管理界面
     */
    hideLevelManager() {
        const levelManagerMenu = DOMUtils.$('#level-manager-menu');
        const mainMenu = DOMUtils.$('#main-menu');
        
        if (levelManagerMenu && mainMenu) {
            DOMUtils.addClass(levelManagerMenu, 'hidden');
            DOMUtils.removeClass(mainMenu, 'hidden');
        }
    }

    /**
     * 切换标签页
     */
    async switchTab(tabType) {
        // 更新标签页状态
        const tabButtons = DOMUtils.$$('.level-manager-tabs .tab-btn');
        tabButtons.forEach(btn => {
            DOMUtils.removeClass(btn, 'active');
            if (btn.dataset.type === tabType) {
                DOMUtils.addClass(btn, 'active');
            }
        });

        this.currentTab = tabType;
        await this.loadLevels();
    }

    /**
     * 加载关卡列表
     */
    async loadLevels() {
        try {
            let levels = [];
            
            if (this.currentTab === 'my') {
                levels = await this.levelManager.getUserLevels();
            } else if (this.currentTab === 'shared') {
                const searchCriteria = {
                    includePrivate: false,
                    sortBy: 'rating'
                };
                levels = await this.levelManager.searchLevels(searchCriteria);
            }

            this.currentLevels = levels;
            this.displayLevels(levels);
        } catch (error) {
            DebugUtils.log(`加载关卡列表失败: ${error.message}`, 'error');
            this.displayLevels([]);
        }
    }

    /**
     * 筛选关卡
     */
    async filterLevels() {
        const searchInput = DOMUtils.$('#level-search');
        const difficultyFilter = DOMUtils.$('#difficulty-filter');
        
        const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
        const difficulty = difficultyFilter ? difficultyFilter.value : '';

        let filteredLevels = this.currentLevels;

        // 按名称和描述搜索
        if (searchTerm) {
            filteredLevels = filteredLevels.filter(level => 
                level.name.toLowerCase().includes(searchTerm) ||
                level.description.toLowerCase().includes(searchTerm) ||
                level.author.toLowerCase().includes(searchTerm)
            );
        }

        // 按难度筛选
        if (difficulty) {
            filteredLevels = filteredLevels.filter(level => level.difficulty === difficulty);
        }

        this.displayLevels(filteredLevels);
    }

    /**
     * 显示关卡列表
     */
    displayLevels(levels) {
        const levelList = DOMUtils.$('#level-list');
        if (!levelList) return;

        // 清空现有列表
        levelList.innerHTML = '';

        if (levels.length === 0) {
            // 显示空状态
            const emptyDiv = document.createElement('div');
            emptyDiv.className = 'empty-level-list';
            emptyDiv.textContent = this.currentTab === 'my' ? '您还没有创建任何关卡' : '没有找到共享关卡';
            levelList.appendChild(emptyDiv);
            return;
        }

        // 生成关卡项目
        levels.forEach(level => {
            const item = this.createLevelItem(level);
            levelList.appendChild(item);
        });
    }

    /**
     * 创建关卡项目元素
     */
    createLevelItem(level) {
        const item = document.createElement('div');
        item.className = 'level-item';

        const createdDate = new Date(level.createdAt).toLocaleDateString();
        const updatedDate = new Date(level.updatedAt).toLocaleDateString();

        // 根据当前标签页显示不同的操作按钮
        let actionButtons = '';
        if (this.currentTab === 'my') {
            actionButtons = `
                <button class="level-action-btn primary" onclick="levelManagerUI.editLevel('${level.id}')">编辑</button>
                <button class="level-action-btn" onclick="levelManagerUI.testLevel('${level.id}')">测试</button>
                <button class="level-action-btn" onclick="levelManagerUI.duplicateLevel('${level.id}')">复制</button>
                <button class="level-action-btn" onclick="levelManagerUI.exportLevel('${level.id}')">导出</button>
                ${level.isPublic ? 
                    `<button class="level-action-btn" onclick="levelManagerUI.unpublishLevel('${level.id}')">取消发布</button>` :
                    `<button class="level-action-btn" onclick="levelManagerUI.publishLevel('${level.id}')">发布</button>`
                }
                <button class="level-action-btn danger" onclick="levelManagerUI.deleteLevel('${level.id}')">删除</button>
            `;
        } else {
            actionButtons = `
                <button class="level-action-btn primary" onclick="levelManagerUI.playLevel('${level.id}')">游玩</button>
                <button class="level-action-btn" onclick="levelManagerUI.duplicateLevel('${level.id}')">复制</button>
                <button class="level-action-btn" onclick="levelManagerUI.rateLevel('${level.id}')">评分</button>
            `;
        }

        item.innerHTML = `
            <div class="level-header">
                <div>
                    <div class="level-title">${level.name}</div>
                    <div class="level-author">作者: ${level.author}</div>
                </div>
                <div class="level-difficulty ${level.difficulty}">${this.getDifficultyText(level.difficulty)}</div>
            </div>
            <div class="level-description">${level.description || '暂无描述'}</div>
            <div class="level-stats">
                <span>游玩次数: ${level.playCount}</span>
                <span>评分: ${level.rating.toFixed(1)} (${level.ratingCount})</span>
                <span>${this.currentTab === 'my' ? '更新' : '创建'}: ${this.currentTab === 'my' ? updatedDate : createdDate}</span>
            </div>
            <div class="level-actions">
                ${actionButtons}
            </div>
        `;

        return item;
    }

    /**
     * 获取难度文本
     */
    getDifficultyText(difficulty) {
        const difficultyMap = {
            easy: '简单',
            normal: '普通',
            hard: '困难',
            expert: '专家'
        };
        return difficultyMap[difficulty] || '普通';
    }

    /**
     * 显示创建关卡对话框
     */
    showCreateLevelDialog() {
        const dialog = DOMUtils.$('#create-level-dialog');
        if (dialog) {
            DOMUtils.removeClass(dialog, 'hidden');
            
            // 清空输入框
            const nameInput = DOMUtils.$('#new-level-name');
            const descInput = DOMUtils.$('#new-level-description');
            if (nameInput) nameInput.value = '';
            if (descInput) descInput.value = '';
            
            // 聚焦到名称输入框
            if (nameInput) nameInput.focus();
        }
    }

    /**
     * 隐藏创建关卡对话框
     */
    hideCreateLevelDialog() {
        const dialog = DOMUtils.$('#create-level-dialog');
        if (dialog) {
            DOMUtils.addClass(dialog, 'hidden');
        }
    }

    /**
     * 创建新关卡
     */
    async createNewLevel() {
        const nameInput = DOMUtils.$('#new-level-name');
        const descInput = DOMUtils.$('#new-level-description');
        
        if (!nameInput || !nameInput.value.trim()) {
            this.showNotification('请输入关卡名称', 'error');
            return;
        }

        const name = nameInput.value.trim();
        const description = descInput ? descInput.value.trim() : '';

        try {
            const success = await this.levelEditor.createNewLevel(name, description);
            
            if (success) {
                this.hideCreateLevelDialog();
                this.hideLevelManager();
                this.showLevelEditor();
                this.showNotification('关卡创建成功，开始编辑', 'success');
            } else {
                this.showNotification('创建关卡失败', 'error');
            }
        } catch (error) {
            DebugUtils.log(`创建关卡失败: ${error.message}`, 'error');
            this.showNotification('创建关卡失败', 'error');
        }
    }

    /**
     * 编辑关卡
     */
    async editLevel(levelId) {
        try {
            const success = await this.levelEditor.loadLevel(levelId);
            
            if (success) {
                this.hideLevelManager();
                this.showLevelEditor();
                this.showNotification('关卡加载成功，开始编辑', 'success');
            } else {
                this.showNotification('加载关卡失败', 'error');
            }
        } catch (error) {
            DebugUtils.log(`编辑关卡失败: ${error.message}`, 'error');
            this.showNotification('编辑关卡失败', 'error');
        }
    }

    /**
     * 显示关卡编辑器
     */
    showLevelEditor() {
        const editorMenu = DOMUtils.$('#level-editor-menu');
        if (editorMenu) {
            DOMUtils.removeClass(editorMenu, 'hidden');
        }
    }

    /**
     * 测试关卡
     */
    async testLevel(levelId) {
        try {
            // 这里应该启动游戏并加载指定关卡
            this.showNotification('测试功能开发中', 'info');
        } catch (error) {
            DebugUtils.log(`测试关卡失败: ${error.message}`, 'error');
            this.showNotification('测试关卡失败', 'error');
        }
    }

    /**
     * 游玩关卡
     */
    async playLevel(levelId) {
        try {
            // 增加游玩次数
            await this.levelManager.incrementPlayCount(levelId);

            // 这里应该启动游戏并加载指定关卡
            this.showNotification('游玩功能开发中', 'info');
        } catch (error) {
            DebugUtils.log(`游玩关卡失败: ${error.message}`, 'error');
            this.showNotification('游玩关卡失败', 'error');
        }
    }

    /**
     * 复制关卡
     */
    async duplicateLevel(levelId) {
        try {
            const level = await this.levelManager.loadLevel(levelId);
            if (!level) {
                this.showNotification('关卡不存在', 'error');
                return;
            }

            const newName = prompt('请输入新关卡名称:', `${level.name} - 副本`);
            if (!newName || !newName.trim()) {
                return;
            }

            const newLevelId = await this.levelManager.duplicateLevel(levelId, newName.trim());

            if (newLevelId) {
                this.showNotification('关卡复制成功', 'success');
                await this.loadLevels(); // 刷新列表
            } else {
                this.showNotification('复制关卡失败', 'error');
            }
        } catch (error) {
            DebugUtils.log(`复制关卡失败: ${error.message}`, 'error');
            this.showNotification('复制关卡失败', 'error');
        }
    }

    /**
     * 导出关卡
     */
    async exportLevel(levelId) {
        try {
            const exportData = await this.levelManager.exportLevel(levelId);

            if (exportData) {
                // 创建下载链接
                const blob = new Blob([exportData], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `level_${levelId}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.showNotification('关卡导出成功', 'success');
            } else {
                this.showNotification('导出关卡失败', 'error');
            }
        } catch (error) {
            DebugUtils.log(`导出关卡失败: ${error.message}`, 'error');
            this.showNotification('导出关卡失败', 'error');
        }
    }

    /**
     * 导入关卡
     */
    async importLevel() {
        try {
            // 创建文件输入元素
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';

            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = async (e) => {
                    try {
                        const importString = e.target.result;
                        const newLevelId = await this.levelManager.importLevel(importString);

                        if (newLevelId) {
                            this.showNotification('关卡导入成功', 'success');
                            await this.loadLevels(); // 刷新列表
                        } else {
                            this.showNotification('导入关卡失败', 'error');
                        }
                    } catch (error) {
                        DebugUtils.log(`导入关卡失败: ${error.message}`, 'error');
                        this.showNotification('导入关卡失败，文件格式错误', 'error');
                    }
                };
                reader.readAsText(file);
            };

            input.click();
        } catch (error) {
            DebugUtils.log(`导入关卡失败: ${error.message}`, 'error');
            this.showNotification('导入关卡失败', 'error');
        }
    }

    /**
     * 发布关卡
     */
    async publishLevel(levelId) {
        try {
            const success = await this.levelManager.publishLevel(levelId);

            if (success) {
                this.showNotification('关卡发布成功', 'success');
                await this.loadLevels(); // 刷新列表
            } else {
                this.showNotification('发布关卡失败', 'error');
            }
        } catch (error) {
            DebugUtils.log(`发布关卡失败: ${error.message}`, 'error');
            this.showNotification('发布关卡失败', 'error');
        }
    }

    /**
     * 取消发布关卡
     */
    async unpublishLevel(levelId) {
        try {
            const success = await this.levelManager.unpublishLevel(levelId);

            if (success) {
                this.showNotification('关卡已设为私有', 'success');
                await this.loadLevels(); // 刷新列表
            } else {
                this.showNotification('取消发布失败', 'error');
            }
        } catch (error) {
            DebugUtils.log(`取消发布失败: ${error.message}`, 'error');
            this.showNotification('取消发布失败', 'error');
        }
    }

    /**
     * 删除关卡
     */
    async deleteLevel(levelId) {
        try {
            const level = await this.levelManager.loadLevel(levelId);
            if (!level) {
                this.showNotification('关卡不存在', 'error');
                return;
            }

            const confirmed = confirm(`确定要删除关卡"${level.name}"吗？此操作不可撤销。`);
            if (!confirmed) {
                return;
            }

            const success = await this.levelManager.deleteLevel(levelId);

            if (success) {
                this.showNotification('关卡删除成功', 'success');
                await this.loadLevels(); // 刷新列表
            } else {
                this.showNotification('删除关卡失败', 'error');
            }
        } catch (error) {
            DebugUtils.log(`删除关卡失败: ${error.message}`, 'error');
            this.showNotification('删除关卡失败', 'error');
        }
    }

    /**
     * 为关卡评分
     */
    async rateLevel(levelId) {
        try {
            const level = await this.levelManager.loadLevel(levelId);
            if (!level) {
                this.showNotification('关卡不存在', 'error');
                return;
            }

            const currentRating = await this.levelManager.getUserRating(levelId);
            const ratingText = currentRating > 0 ? `当前评分: ${currentRating}星\n` : '';

            const rating = prompt(`${ratingText}请为关卡"${level.name}"评分 (1-5星):`);
            if (!rating) {
                return;
            }

            const ratingNum = parseInt(rating);
            if (isNaN(ratingNum) || ratingNum < 1 || ratingNum > 5) {
                this.showNotification('请输入1-5之间的数字', 'error');
                return;
            }

            const success = await this.levelManager.rateLevel(levelId, ratingNum);

            if (success) {
                this.showNotification(`评分成功: ${ratingNum}星`, 'success');
                await this.loadLevels(); // 刷新列表
            } else {
                this.showNotification('评分失败', 'error');
            }
        } catch (error) {
            DebugUtils.log(`评分失败: ${error.message}`, 'error');
            this.showNotification('评分失败', 'error');
        }
    }

    /**
     * 显示关卡设置对话框
     */
    showLevelSettingsDialog(level) {
        const dialog = DOMUtils.$('#level-settings-dialog');
        if (!dialog || !level) return;

        // 填充当前关卡信息
        const nameInput = DOMUtils.$('#level-name-input');
        const descInput = DOMUtils.$('#level-description-input');
        const difficultySelect = DOMUtils.$('#level-difficulty-select');
        const sparksInput = DOMUtils.$('#sparks-required-input');
        const timeLimitInput = DOMUtils.$('#time-limit-input');
        const publicCheckbox = DOMUtils.$('#level-public-checkbox');

        if (nameInput) nameInput.value = level.name;
        if (descInput) descInput.value = level.description;
        if (difficultySelect) difficultySelect.value = level.difficulty;
        if (sparksInput) sparksInput.value = level.data.winConditions.sparksRequired;
        if (timeLimitInput) timeLimitInput.value = level.data.winConditions.timeLimit;
        if (publicCheckbox) publicCheckbox.checked = level.isPublic;

        DOMUtils.removeClass(dialog, 'hidden');
    }

    /**
     * 隐藏关卡设置对话框
     */
    hideLevelSettingsDialog() {
        const dialog = DOMUtils.$('#level-settings-dialog');
        if (dialog) {
            DOMUtils.addClass(dialog, 'hidden');
        }
    }

    /**
     * 保存关卡设置
     */
    async saveLevelSettings() {
        try {
            if (!this.levelEditor.currentLevel) {
                this.showNotification('没有当前关卡', 'error');
                return;
            }

            const nameInput = DOMUtils.$('#level-name-input');
            const descInput = DOMUtils.$('#level-description-input');
            const difficultySelect = DOMUtils.$('#level-difficulty-select');
            const sparksInput = DOMUtils.$('#sparks-required-input');
            const timeLimitInput = DOMUtils.$('#time-limit-input');
            const publicCheckbox = DOMUtils.$('#level-public-checkbox');

            if (!nameInput || !nameInput.value.trim()) {
                this.showNotification('请输入关卡名称', 'error');
                return;
            }

            // 更新关卡信息
            const level = this.levelEditor.currentLevel;
            level.name = nameInput.value.trim();
            level.description = descInput ? descInput.value.trim() : '';
            level.difficulty = difficultySelect ? difficultySelect.value : 'normal';
            level.isPublic = publicCheckbox ? publicCheckbox.checked : false;

            if (sparksInput) {
                level.data.winConditions.sparksRequired = parseInt(sparksInput.value) || 10;
            }
            if (timeLimitInput) {
                level.data.winConditions.timeLimit = parseInt(timeLimitInput.value) || 0;
            }

            const success = await this.levelEditor.saveLevel();

            if (success) {
                this.hideLevelSettingsDialog();
                this.showNotification('关卡设置保存成功', 'success');
            } else {
                this.showNotification('保存关卡设置失败', 'error');
            }
        } catch (error) {
            DebugUtils.log(`保存关卡设置失败: ${error.message}`, 'error');
            this.showNotification('保存关卡设置失败', 'error');
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 这里可以实现通知显示逻辑
        console.log(`[${type.toUpperCase()}] ${message}`);

        // 简单的实现，后续可以改为更美观的通知组件
        if (type === 'error') {
            console.error(message);
        } else if (type === 'success') {
            console.log(message);
        }
    }
}

// 创建全局关卡管理器UI实例
window.levelManagerUI = new LevelManagerUI();
