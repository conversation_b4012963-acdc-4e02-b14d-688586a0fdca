/**
 * 排行榜系统 - SplitSecond Spark
 * 基于KV存储实现本地排行榜功能，支持分数记录、排序和显示
 */

class LeaderboardManager {
    constructor(kvService) {
        this.kvService = kvService || window.kvStorage;
        
        // 存储键定义
        this.storageKeys = {
            globalLeaderboard: 'leaderboard:global',
            accountLeaderboard: 'leaderboard:account:',
            dailyLeaderboard: 'leaderboard:daily:',
            weeklyLeaderboard: 'leaderboard:weekly:'
        };

        // 排行榜类型定义
        this.leaderboardTypes = {
            GLOBAL: 'global',           // 全局排行榜
            ACCOUNT: 'account',         // 账号个人记录
            DAILY: 'daily',            // 每日排行榜
            WEEKLY: 'weekly'           // 每周排行榜
        };

        // 分数记录结构
        this.defaultScoreRecord = {
            id: null,
            accountId: null,
            accountName: '',
            score: 0,
            level: 1,
            sparksCollected: 0,
            dimensionSwitches: 0,
            playTime: 0,
            timestamp: null,
            date: null
        };

        DebugUtils.log('排行榜系统初始化完成');
    }

    /**
     * 生成分数记录ID
     * @returns {string} 记录ID
     */
    generateRecordId() {
        return 'score_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 获取当前日期字符串
     * @returns {string} 日期字符串 (YYYY-MM-DD)
     */
    getCurrentDateString() {
        const now = new Date();
        return now.getFullYear() + '-' + 
               String(now.getMonth() + 1).padStart(2, '0') + '-' + 
               String(now.getDate()).padStart(2, '0');
    }

    /**
     * 获取当前周字符串
     * @returns {string} 周字符串 (YYYY-WW)
     */
    getCurrentWeekString() {
        const now = new Date();
        const startOfYear = new Date(now.getFullYear(), 0, 1);
        const pastDaysOfYear = (now - startOfYear) / ********;
        const weekNumber = Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
        return now.getFullYear() + '-W' + String(weekNumber).padStart(2, '0');
    }

    /**
     * 提交分数记录
     * @param {object} scoreData - 分数数据
     * @returns {Promise<boolean>} 是否成功
     */
    async submitScore(scoreData) {
        try {
            const accountManager = window.accountManager;
            const currentAccount = await accountManager.getCurrentAccount();
            
            if (!currentAccount) {
                DebugUtils.log('没有当前账号，无法提交分数', 'error');
                return false;
            }

            // 创建分数记录
            const recordId = this.generateRecordId();
            const currentDate = this.getCurrentDateString();
            const currentWeek = this.getCurrentWeekString();

            const scoreRecord = {
                ...this.defaultScoreRecord,
                id: recordId,
                accountId: currentAccount.id,
                accountName: currentAccount.name,
                score: scoreData.score || 0,
                level: scoreData.level || 1,
                sparksCollected: scoreData.sparksCollected || 0,
                dimensionSwitches: scoreData.dimensionSwitches || 0,
                playTime: scoreData.playTime || 0,
                timestamp: Date.now(),
                date: currentDate
            };

            // 提交到各个排行榜
            const results = await Promise.all([
                this.addToGlobalLeaderboard(scoreRecord),
                this.addToAccountLeaderboard(scoreRecord),
                this.addToDailyLeaderboard(scoreRecord, currentDate),
                this.addToWeeklyLeaderboard(scoreRecord, currentWeek)
            ]);

            const success = results.every(result => result);
            
            if (success) {
                DebugUtils.log(`分数提交成功: ${scoreRecord.score}分`);
                
                // 更新账号统计
                await this.updateAccountStats(currentAccount.id, scoreRecord);
            }

            return success;
        } catch (error) {
            DebugUtils.log(`提交分数失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 添加到全局排行榜
     * @param {object} scoreRecord - 分数记录
     * @returns {Promise<boolean>} 是否成功
     */
    async addToGlobalLeaderboard(scoreRecord) {
        try {
            const leaderboard = await this.getLeaderboard(this.leaderboardTypes.GLOBAL) || [];
            
            // 添加新记录
            leaderboard.push(scoreRecord);
            
            // 按分数排序（降序）
            leaderboard.sort((a, b) => b.score - a.score);
            
            // 保留前100名
            const topRecords = leaderboard.slice(0, 100);
            
            return await this.kvService.put(this.storageKeys.globalLeaderboard, topRecords);
        } catch (error) {
            DebugUtils.log(`添加到全局排行榜失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 添加到账号个人排行榜
     * @param {object} scoreRecord - 分数记录
     * @returns {Promise<boolean>} 是否成功
     */
    async addToAccountLeaderboard(scoreRecord) {
        try {
            const key = this.storageKeys.accountLeaderboard + scoreRecord.accountId;
            const accountRecords = await this.kvService.get(key) || [];
            
            // 添加新记录
            accountRecords.push(scoreRecord);
            
            // 按分数排序（降序）
            accountRecords.sort((a, b) => b.score - a.score);
            
            // 保留前50名个人记录
            const topRecords = accountRecords.slice(0, 50);
            
            return await this.kvService.put(key, topRecords);
        } catch (error) {
            DebugUtils.log(`添加到账号排行榜失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 添加到每日排行榜
     * @param {object} scoreRecord - 分数记录
     * @param {string} dateString - 日期字符串
     * @returns {Promise<boolean>} 是否成功
     */
    async addToDailyLeaderboard(scoreRecord, dateString) {
        try {
            const key = this.storageKeys.dailyLeaderboard + dateString;
            const dailyRecords = await this.kvService.get(key) || [];
            
            // 添加新记录
            dailyRecords.push(scoreRecord);
            
            // 按分数排序（降序）
            dailyRecords.sort((a, b) => b.score - a.score);
            
            // 保留前50名
            const topRecords = dailyRecords.slice(0, 50);
            
            return await this.kvService.put(key, topRecords);
        } catch (error) {
            DebugUtils.log(`添加到每日排行榜失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 添加到每周排行榜
     * @param {object} scoreRecord - 分数记录
     * @param {string} weekString - 周字符串
     * @returns {Promise<boolean>} 是否成功
     */
    async addToWeeklyLeaderboard(scoreRecord, weekString) {
        try {
            const key = this.storageKeys.weeklyLeaderboard + weekString;
            const weeklyRecords = await this.kvService.get(key) || [];
            
            // 添加新记录
            weeklyRecords.push(scoreRecord);
            
            // 按分数排序（降序）
            weeklyRecords.sort((a, b) => b.score - a.score);
            
            // 保留前50名
            const topRecords = weeklyRecords.slice(0, 50);
            
            return await this.kvService.put(key, topRecords);
        } catch (error) {
            DebugUtils.log(`添加到每周排行榜失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 获取排行榜数据
     * @param {string} type - 排行榜类型
     * @param {string} identifier - 标识符（日期、周、账号ID等）
     * @returns {Promise<Array>} 排行榜数据
     */
    async getLeaderboard(type, identifier = null) {
        try {
            let key;
            
            switch (type) {
                case this.leaderboardTypes.GLOBAL:
                    key = this.storageKeys.globalLeaderboard;
                    break;
                case this.leaderboardTypes.ACCOUNT:
                    if (!identifier) return [];
                    key = this.storageKeys.accountLeaderboard + identifier;
                    break;
                case this.leaderboardTypes.DAILY:
                    const dateString = identifier || this.getCurrentDateString();
                    key = this.storageKeys.dailyLeaderboard + dateString;
                    break;
                case this.leaderboardTypes.WEEKLY:
                    const weekString = identifier || this.getCurrentWeekString();
                    key = this.storageKeys.weeklyLeaderboard + weekString;
                    break;
                default:
                    DebugUtils.log(`未知的排行榜类型: ${type}`, 'error');
                    return [];
            }

            const leaderboard = await this.kvService.get(key);
            return leaderboard || [];
        } catch (error) {
            DebugUtils.log(`获取排行榜数据失败: ${error.message}`, 'error');
            return [];
        }
    }

    /**
     * 获取账号在排行榜中的排名
     * @param {string} accountId - 账号ID
     * @param {string} type - 排行榜类型
     * @param {string} identifier - 标识符
     * @returns {Promise<object|null>} 排名信息
     */
    async getAccountRank(accountId, type, identifier = null) {
        try {
            const leaderboard = await this.getLeaderboard(type, identifier);
            
            // 找到账号的最高分记录
            let bestRecord = null;
            let rank = -1;
            
            for (let i = 0; i < leaderboard.length; i++) {
                if (leaderboard[i].accountId === accountId) {
                    if (!bestRecord || leaderboard[i].score > bestRecord.score) {
                        bestRecord = leaderboard[i];
                        rank = i + 1;
                    }
                }
            }

            return bestRecord ? { rank, record: bestRecord, total: leaderboard.length } : null;
        } catch (error) {
            DebugUtils.log(`获取账号排名失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 更新账号统计信息
     * @param {string} accountId - 账号ID
     * @param {object} scoreRecord - 分数记录
     * @returns {Promise<boolean>} 是否成功
     */
    async updateAccountStats(accountId, scoreRecord) {
        try {
            const accountManager = window.accountManager;
            const account = await accountManager.getAccount(accountId);
            
            if (!account) return false;

            const updatedStats = {
                ...account.gameStats,
                totalSparks: account.gameStats.totalSparks + scoreRecord.sparksCollected,
                dimensionSwitches: account.gameStats.dimensionSwitches + scoreRecord.dimensionSwitches,
                totalPlayTime: account.gameStats.totalPlayTime + scoreRecord.playTime
            };

            // 更新最高分
            if (scoreRecord.score > account.gameStats.bestScore) {
                updatedStats.bestScore = scoreRecord.score;
            }

            return await accountManager.updateAccount(accountId, { gameStats: updatedStats });
        } catch (error) {
            DebugUtils.log(`更新账号统计失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 清理过期的排行榜数据
     * @param {number} daysToKeep - 保留天数
     * @returns {Promise<boolean>} 是否成功
     */
    async cleanupOldLeaderboards(daysToKeep = 30) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
            
            // 获取所有排行榜键
            const allKeys = await this.kvService.list('leaderboard:');
            
            let deletedCount = 0;
            
            for (const key of allKeys) {
                if (key.includes('daily:') || key.includes('weekly:')) {
                    // 提取日期或周信息
                    const parts = key.split(':');
                    const dateOrWeek = parts[parts.length - 1];
                    
                    // 检查是否过期（简化处理）
                    if (dateOrWeek < cutoffDate.toISOString().split('T')[0]) {
                        await this.kvService.delete(key);
                        deletedCount++;
                    }
                }
            }

            DebugUtils.log(`清理了 ${deletedCount} 个过期排行榜`);
            return true;
        } catch (error) {
            DebugUtils.log(`清理排行榜数据失败: ${error.message}`, 'error');
            return false;
        }
    }
}

// 创建全局排行榜管理器实例
window.leaderboardManager = new LeaderboardManager();
