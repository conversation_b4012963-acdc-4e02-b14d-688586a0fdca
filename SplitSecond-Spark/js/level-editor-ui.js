/**
 * 关卡编辑器UI控制器 - SplitSecond Spark
 * 处理关卡编辑器界面的交互逻辑
 */

class LevelEditorUI {
    constructor() {
        this.levelEditor = window.levelEditor;
        this.levelManagerUI = window.levelManagerUI;
        
        this.initializeEventListeners();
        
        DebugUtils.log('关卡编辑器UI初始化完成');
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 工具按钮
        const toolButtons = DOMUtils.$$('.tool-btn');
        toolButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tool = e.target.dataset.tool;
                this.selectTool(tool);
            });
        });

        // 维度按钮
        const dimensionButtons = DOMUtils.$$('.dim-btn');
        dimensionButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const dimension = e.target.dataset.dimension;
                this.selectDimension(dimension);
            });
        });

        // 操作按钮
        const saveLevelBtn = DOMUtils.$('#save-level-btn');
        if (saveLevelBtn) {
            saveLevelBtn.addEventListener('click', () => this.saveLevel());
        }

        const testLevelBtn = DOMUtils.$('#test-level-btn');
        if (testLevelBtn) {
            testLevelBtn.addEventListener('click', () => this.testLevel());
        }

        const levelSettingsBtn = DOMUtils.$('#level-settings-btn');
        if (levelSettingsBtn) {
            levelSettingsBtn.addEventListener('click', () => this.showLevelSettings());
        }

        const editorBackBtn = DOMUtils.$('#editor-back-btn');
        if (editorBackBtn) {
            editorBackBtn.addEventListener('click', () => this.exitEditor());
        }

        // ESC键退出编辑器
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const editorMenu = DOMUtils.$('#level-editor-menu');
                if (editorMenu && !DOMUtils.hasClass(editorMenu, 'hidden')) {
                    this.exitEditor();
                }
            }
        });

        // 监听编辑器状态变化
        this.setupEditorStateListeners();
    }

    /**
     * 设置编辑器状态监听器
     */
    setupEditorStateListeners() {
        // 监听工具变化
        if (this.levelEditor) {
            // 这里可以添加编辑器状态变化的监听
        }
    }

    /**
     * 选择工具
     */
    selectTool(tool) {
        if (!this.levelEditor) return;

        // 更新工具按钮状态
        const toolButtons = DOMUtils.$$('.tool-btn');
        toolButtons.forEach(btn => {
            DOMUtils.removeClass(btn, 'active');
            if (btn.dataset.tool === tool) {
                DOMUtils.addClass(btn, 'active');
            }
        });

        // 设置编辑器工具
        this.levelEditor.setTool(tool);
        
        DebugUtils.log(`选择工具: ${tool}`);
    }

    /**
     * 选择时间维度
     */
    selectDimension(dimension) {
        if (!this.levelEditor) return;

        // 更新维度按钮状态
        const dimensionButtons = DOMUtils.$$('.dim-btn');
        dimensionButtons.forEach(btn => {
            DOMUtils.removeClass(btn, 'active');
            if (btn.dataset.dimension === dimension) {
                DOMUtils.addClass(btn, 'active');
            }
        });

        // 设置编辑器维度
        this.levelEditor.setDimension(dimension);
        
        DebugUtils.log(`选择时间维度: ${dimension}`);
    }

    /**
     * 保存关卡
     */
    async saveLevel() {
        if (!this.levelEditor || !this.levelEditor.currentLevel) {
            this.showNotification('没有当前关卡可保存', 'error');
            return;
        }

        try {
            const success = await this.levelEditor.saveLevel();
            
            if (success) {
                this.showNotification('关卡保存成功', 'success');
            } else {
                this.showNotification('保存关卡失败', 'error');
            }
        } catch (error) {
            DebugUtils.log(`保存关卡失败: ${error.message}`, 'error');
            this.showNotification('保存关卡失败', 'error');
        }
    }

    /**
     * 测试关卡
     */
    async testLevel() {
        if (!this.levelEditor || !this.levelEditor.currentLevel) {
            this.showNotification('没有当前关卡可测试', 'error');
            return;
        }

        try {
            // 先保存关卡
            const saved = await this.levelEditor.saveLevel();
            if (!saved) {
                this.showNotification('保存关卡失败，无法测试', 'error');
                return;
            }

            // 这里应该启动游戏并加载当前关卡进行测试
            this.showNotification('测试功能开发中', 'info');
            
            DebugUtils.log('开始测试关卡');
        } catch (error) {
            DebugUtils.log(`测试关卡失败: ${error.message}`, 'error');
            this.showNotification('测试关卡失败', 'error');
        }
    }

    /**
     * 显示关卡设置
     */
    showLevelSettings() {
        if (!this.levelEditor || !this.levelEditor.currentLevel) {
            this.showNotification('没有当前关卡', 'error');
            return;
        }

        if (this.levelManagerUI) {
            this.levelManagerUI.showLevelSettingsDialog(this.levelEditor.currentLevel);
        }
    }

    /**
     * 退出编辑器
     */
    exitEditor() {
        const confirmed = this.confirmExit();
        if (!confirmed) {
            return;
        }

        // 隐藏编辑器界面
        const editorMenu = DOMUtils.$('#level-editor-menu');
        if (editorMenu) {
            DOMUtils.addClass(editorMenu, 'hidden');
        }

        // 显示关卡管理界面
        if (this.levelManagerUI) {
            this.levelManagerUI.showLevelManager();
        } else {
            // 如果没有关卡管理器，返回主菜单
            const mainMenu = DOMUtils.$('#main-menu');
            if (mainMenu) {
                DOMUtils.removeClass(mainMenu, 'hidden');
            }
        }

        // 重置编辑器状态
        if (this.levelEditor) {
            this.levelEditor.currentLevel = null;
            this.levelEditor.isEditing = false;
        }

        DebugUtils.log('退出关卡编辑器');
    }

    /**
     * 确认退出
     */
    confirmExit() {
        if (!this.levelEditor || !this.levelEditor.currentLevel) {
            return true;
        }

        // 这里可以检查是否有未保存的更改
        const hasUnsavedChanges = false; // 简化实现，实际应该检查编辑器状态
        
        if (hasUnsavedChanges) {
            return confirm('您有未保存的更改，确定要退出吗？');
        }

        return true;
    }

    /**
     * 更新编辑器UI状态
     */
    updateEditorUI() {
        if (!this.levelEditor) return;

        // 更新工具选择状态
        const currentTool = this.levelEditor.selectedTool;
        const toolButtons = DOMUtils.$$('.tool-btn');
        toolButtons.forEach(btn => {
            DOMUtils.removeClass(btn, 'active');
            if (btn.dataset.tool === currentTool) {
                DOMUtils.addClass(btn, 'active');
            }
        });

        // 更新维度选择状态
        const currentDimension = this.levelEditor.selectedDimension;
        const dimensionButtons = DOMUtils.$$('.dim-btn');
        dimensionButtons.forEach(btn => {
            DOMUtils.removeClass(btn, 'active');
            if (btn.dataset.dimension === currentDimension) {
                DOMUtils.addClass(btn, 'active');
            }
        });

        // 更新关卡信息显示
        this.updateLevelInfo();
    }

    /**
     * 更新关卡信息显示
     */
    updateLevelInfo() {
        if (!this.levelEditor || !this.levelEditor.currentLevel) return;

        const level = this.levelEditor.currentLevel;
        
        // 这里可以更新界面上的关卡信息显示
        // 比如关卡名称、描述等
        
        DebugUtils.log(`当前编辑关卡: ${level.name}`);
    }

    /**
     * 处理编辑器快捷键
     */
    handleEditorShortcuts(e) {
        if (!this.levelEditor || !this.levelEditor.isEditing) return;

        // 工具快捷键
        switch (e.key) {
            case '1':
                this.selectTool('select');
                break;
            case '2':
                this.selectTool('platform');
                break;
            case '3':
                this.selectTool('spark');
                break;
            case '4':
                this.selectTool('obstacle');
                break;
            case '5':
                this.selectTool('portal');
                break;
            case '6':
                this.selectTool('trigger');
                break;
            case '7':
                this.selectTool('playerStart');
                break;
            case '8':
                this.selectTool('eraser');
                break;
            
            // 维度快捷键
            case 'q':
                this.selectDimension('past');
                break;
            case 'w':
                this.selectDimension('present');
                break;
            case 'e':
                this.selectDimension('future');
                break;
        }
    }

    /**
     * 显示编辑器帮助
     */
    showEditorHelp() {
        const helpText = `
关卡编辑器快捷键:
数字键 1-8: 选择工具
Q/W/E: 切换时间维度
G: 切换网格显示
Delete: 删除选中对象
Ctrl+C: 复制选中对象
Ctrl+V: 粘贴对象
Ctrl+S: 保存关卡
ESC: 退出编辑器

鼠标操作:
左键: 放置/选择对象
拖拽: 移动对象
滚轮: 缩放视图
        `;
        
        alert(helpText);
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 这里可以实现通知显示逻辑
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // 简单的实现，后续可以改为更美观的通知组件
        if (type === 'error') {
            console.error(message);
        } else if (type === 'success') {
            console.log(message);
        }
    }
}

// 创建全局关卡编辑器UI实例
window.levelEditorUI = new LevelEditorUI();
