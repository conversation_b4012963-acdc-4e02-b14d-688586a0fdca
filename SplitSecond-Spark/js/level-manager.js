/**
 * 关卡管理系统 - SplitSecond Spark
 * 实现关卡创建、编辑、保存、加载、分享和评分功能
 */

class LevelManager {
    constructor(kvService) {
        this.kvService = kvService || window.kvStorage;
        
        // 存储键定义
        this.storageKeys = {
            customLevels: 'levels:custom:',
            sharedLevels: 'levels:shared:',
            levelRatings: 'levels:ratings:',
            levelMetadata: 'levels:metadata'
        };

        // 关卡难度定义
        this.difficulties = {
            EASY: 'easy',
            NORMAL: 'normal',
            HARD: 'hard',
            EXPERT: 'expert'
        };

        // 默认关卡结构
        this.defaultLevel = {
            id: null,
            name: '新关卡',
            description: '',
            author: '',
            authorId: '',
            difficulty: this.difficulties.NORMAL,
            version: '1.0.0',
            createdAt: null,
            updatedAt: null,
            playCount: 0,
            rating: 0,
            ratingCount: 0,
            tags: [],
            isPublic: false,
            
            // 关卡数据
            data: {
                width: 800,
                height: 600,
                playerStart: { x: 100, y: 500 },
                
                // 时间维度配置
                dimensions: {
                    past: { enabled: true, color: '#8B4513' },
                    present: { enabled: true, color: '#4169E1' },
                    future: { enabled: true, color: '#9932CC' }
                },
                
                // 游戏对象
                platforms: [],
                sparks: [],
                obstacles: [],
                portals: [],
                triggers: [],
                
                // 胜利条件
                winConditions: {
                    sparksRequired: 10,
                    timeLimit: 0, // 0表示无时间限制
                    reachTarget: null // 目标位置
                }
            }
        };

        DebugUtils.log('关卡管理系统初始化完成');
    }

    /**
     * 生成关卡ID
     * @returns {string} 关卡ID
     */
    generateLevelId() {
        return 'level_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 创建新关卡
     * @param {string} name - 关卡名称
     * @param {string} description - 关卡描述
     * @returns {Promise<object|null>} 创建的关卡对象或null
     */
    async createLevel(name, description = '') {
        try {
            const accountManager = window.accountManager;
            const currentAccount = await accountManager.getCurrentAccount();
            
            if (!currentAccount) {
                DebugUtils.log('没有当前账号，无法创建关卡', 'error');
                return null;
            }

            const levelId = this.generateLevelId();
            const now = Date.now();

            const level = {
                ...this.defaultLevel,
                id: levelId,
                name: name.trim(),
                description: description.trim(),
                author: currentAccount.name,
                authorId: currentAccount.id,
                createdAt: now,
                updatedAt: now
            };

            // 保存关卡
            const success = await this.saveLevel(level);
            
            if (success) {
                DebugUtils.log(`关卡创建成功: ${name} (${levelId})`);
                return level;
            } else {
                DebugUtils.log('关卡创建失败', 'error');
                return null;
            }
        } catch (error) {
            DebugUtils.log(`创建关卡失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 保存关卡
     * @param {object} level - 关卡对象
     * @returns {Promise<boolean>} 是否成功
     */
    async saveLevel(level) {
        try {
            if (!level.id) {
                DebugUtils.log('关卡ID不能为空', 'error');
                return false;
            }

            // 更新修改时间
            level.updatedAt = Date.now();

            // 保存关卡数据
            const levelKey = `${this.storageKeys.customLevels}${level.id}`;
            const success = await this.kvService.put(levelKey, level);

            if (success) {
                // 更新关卡元数据
                await this.updateLevelMetadata();
                DebugUtils.log(`关卡保存成功: ${level.name}`);
            }

            return success;
        } catch (error) {
            DebugUtils.log(`保存关卡失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 加载关卡
     * @param {string} levelId - 关卡ID
     * @returns {Promise<object|null>} 关卡对象或null
     */
    async loadLevel(levelId) {
        try {
            const levelKey = `${this.storageKeys.customLevels}${levelId}`;
            const level = await this.kvService.get(levelKey);
            
            if (level) {
                DebugUtils.log(`关卡加载成功: ${level.name}`);
            }
            
            return level;
        } catch (error) {
            DebugUtils.log(`加载关卡失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 删除关卡
     * @param {string} levelId - 关卡ID
     * @returns {Promise<boolean>} 是否成功
     */
    async deleteLevel(levelId) {
        try {
            const level = await this.loadLevel(levelId);
            if (!level) {
                DebugUtils.log('关卡不存在', 'error');
                return false;
            }

            // 检查权限
            const accountManager = window.accountManager;
            const currentAccount = await accountManager.getCurrentAccount();
            
            if (!currentAccount || level.authorId !== currentAccount.id) {
                DebugUtils.log('没有权限删除此关卡', 'error');
                return false;
            }

            // 删除关卡数据
            const levelKey = `${this.storageKeys.customLevels}${levelId}`;
            const success = await this.kvService.delete(levelKey);

            if (success) {
                // 删除相关评分数据
                const ratingKey = `${this.storageKeys.levelRatings}${levelId}`;
                await this.kvService.delete(ratingKey);
                
                // 更新关卡元数据
                await this.updateLevelMetadata();
                
                DebugUtils.log(`关卡删除成功: ${level.name}`);
            }

            return success;
        } catch (error) {
            DebugUtils.log(`删除关卡失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 获取用户的自定义关卡列表
     * @param {string} accountId - 账号ID（可选，默认当前账号）
     * @returns {Promise<Array>} 关卡列表
     */
    async getUserLevels(accountId = null) {
        try {
            const accountManager = window.accountManager;
            const targetAccountId = accountId || (await accountManager.getCurrentAccount())?.id;
            
            if (!targetAccountId) {
                return [];
            }

            const allLevels = await this.getAllLevels();
            const userLevels = allLevels.filter(level => level.authorId === targetAccountId);
            
            // 按更新时间排序
            userLevels.sort((a, b) => b.updatedAt - a.updatedAt);
            
            return userLevels;
        } catch (error) {
            DebugUtils.log(`获取用户关卡列表失败: ${error.message}`, 'error');
            return [];
        }
    }

    /**
     * 获取所有自定义关卡
     * @returns {Promise<Array>} 关卡列表
     */
    async getAllLevels() {
        try {
            const levelKeys = await this.kvService.list(this.storageKeys.customLevels);
            const levels = [];

            for (const key of levelKeys) {
                const level = await this.kvService.get(key);
                if (level) {
                    levels.push(level);
                }
            }

            return levels;
        } catch (error) {
            DebugUtils.log(`获取关卡列表失败: ${error.message}`, 'error');
            return [];
        }
    }

    /**
     * 搜索关卡
     * @param {object} criteria - 搜索条件
     * @returns {Promise<Array>} 搜索结果
     */
    async searchLevels(criteria) {
        try {
            const allLevels = await this.getAllLevels();
            let results = allLevels;

            // 按名称搜索
            if (criteria.name) {
                const searchName = criteria.name.toLowerCase();
                results = results.filter(level => 
                    level.name.toLowerCase().includes(searchName) ||
                    level.description.toLowerCase().includes(searchName)
                );
            }

            // 按作者搜索
            if (criteria.author) {
                const searchAuthor = criteria.author.toLowerCase();
                results = results.filter(level => 
                    level.author.toLowerCase().includes(searchAuthor)
                );
            }

            // 按难度筛选
            if (criteria.difficulty) {
                results = results.filter(level => level.difficulty === criteria.difficulty);
            }

            // 按标签筛选
            if (criteria.tags && criteria.tags.length > 0) {
                results = results.filter(level => 
                    criteria.tags.some(tag => level.tags.includes(tag))
                );
            }

            // 只显示公开关卡（除非是作者本人）
            if (!criteria.includePrivate) {
                const accountManager = window.accountManager;
                const currentAccount = await accountManager.getCurrentAccount();
                const currentAccountId = currentAccount?.id;
                
                results = results.filter(level => 
                    level.isPublic || level.authorId === currentAccountId
                );
            }

            // 排序
            if (criteria.sortBy) {
                switch (criteria.sortBy) {
                    case 'name':
                        results.sort((a, b) => a.name.localeCompare(b.name));
                        break;
                    case 'rating':
                        results.sort((a, b) => b.rating - a.rating);
                        break;
                    case 'playCount':
                        results.sort((a, b) => b.playCount - a.playCount);
                        break;
                    case 'created':
                        results.sort((a, b) => b.createdAt - a.createdAt);
                        break;
                    case 'updated':
                    default:
                        results.sort((a, b) => b.updatedAt - a.updatedAt);
                        break;
                }
            }

            return results;
        } catch (error) {
            DebugUtils.log(`搜索关卡失败: ${error.message}`, 'error');
            return [];
        }
    }

    /**
     * 为关卡评分
     * @param {string} levelId - 关卡ID
     * @param {number} rating - 评分 (1-5)
     * @returns {Promise<boolean>} 是否成功
     */
    async rateLevel(levelId, rating) {
        try {
            if (rating < 1 || rating > 5) {
                DebugUtils.log('评分必须在1-5之间', 'error');
                return false;
            }

            const accountManager = window.accountManager;
            const currentAccount = await accountManager.getCurrentAccount();

            if (!currentAccount) {
                DebugUtils.log('没有当前账号，无法评分', 'error');
                return false;
            }

            const level = await this.loadLevel(levelId);
            if (!level) {
                DebugUtils.log('关卡不存在', 'error');
                return false;
            }

            // 不能给自己的关卡评分
            if (level.authorId === currentAccount.id) {
                DebugUtils.log('不能给自己的关卡评分', 'error');
                return false;
            }

            // 获取现有评分数据
            const ratingKey = `${this.storageKeys.levelRatings}${levelId}`;
            const ratingsData = await this.kvService.get(ratingKey) || {
                ratings: {},
                totalRating: 0,
                ratingCount: 0
            };

            // 更新评分
            const oldRating = ratingsData.ratings[currentAccount.id] || 0;
            ratingsData.ratings[currentAccount.id] = rating;

            // 重新计算总评分
            if (oldRating === 0) {
                // 新评分
                ratingsData.totalRating += rating;
                ratingsData.ratingCount += 1;
            } else {
                // 更新评分
                ratingsData.totalRating = ratingsData.totalRating - oldRating + rating;
            }

            // 计算平均评分
            const averageRating = ratingsData.ratingCount > 0 ?
                ratingsData.totalRating / ratingsData.ratingCount : 0;

            // 保存评分数据
            await this.kvService.put(ratingKey, ratingsData);

            // 更新关卡的评分信息
            level.rating = Math.round(averageRating * 10) / 10; // 保留一位小数
            level.ratingCount = ratingsData.ratingCount;
            await this.saveLevel(level);

            DebugUtils.log(`关卡评分成功: ${level.name} - ${rating}星`);
            return true;
        } catch (error) {
            DebugUtils.log(`关卡评分失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 获取用户对关卡的评分
     * @param {string} levelId - 关卡ID
     * @param {string} accountId - 账号ID（可选，默认当前账号）
     * @returns {Promise<number>} 评分 (0表示未评分)
     */
    async getUserRating(levelId, accountId = null) {
        try {
            const accountManager = window.accountManager;
            const targetAccountId = accountId || (await accountManager.getCurrentAccount())?.id;

            if (!targetAccountId) {
                return 0;
            }

            const ratingKey = `${this.storageKeys.levelRatings}${levelId}`;
            const ratingsData = await this.kvService.get(ratingKey);

            return ratingsData?.ratings[targetAccountId] || 0;
        } catch (error) {
            DebugUtils.log(`获取用户评分失败: ${error.message}`, 'error');
            return 0;
        }
    }

    /**
     * 增加关卡游玩次数
     * @param {string} levelId - 关卡ID
     * @returns {Promise<boolean>} 是否成功
     */
    async incrementPlayCount(levelId) {
        try {
            const level = await this.loadLevel(levelId);
            if (!level) {
                return false;
            }

            level.playCount = (level.playCount || 0) + 1;
            return await this.saveLevel(level);
        } catch (error) {
            DebugUtils.log(`增加游玩次数失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 发布关卡（设为公开）
     * @param {string} levelId - 关卡ID
     * @returns {Promise<boolean>} 是否成功
     */
    async publishLevel(levelId) {
        try {
            const level = await this.loadLevel(levelId);
            if (!level) {
                DebugUtils.log('关卡不存在', 'error');
                return false;
            }

            // 检查权限
            const accountManager = window.accountManager;
            const currentAccount = await accountManager.getCurrentAccount();

            if (!currentAccount || level.authorId !== currentAccount.id) {
                DebugUtils.log('没有权限发布此关卡', 'error');
                return false;
            }

            level.isPublic = true;
            const success = await this.saveLevel(level);

            if (success) {
                DebugUtils.log(`关卡发布成功: ${level.name}`);
            }

            return success;
        } catch (error) {
            DebugUtils.log(`发布关卡失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 取消发布关卡（设为私有）
     * @param {string} levelId - 关卡ID
     * @returns {Promise<boolean>} 是否成功
     */
    async unpublishLevel(levelId) {
        try {
            const level = await this.loadLevel(levelId);
            if (!level) {
                DebugUtils.log('关卡不存在', 'error');
                return false;
            }

            // 检查权限
            const accountManager = window.accountManager;
            const currentAccount = await accountManager.getCurrentAccount();

            if (!currentAccount || level.authorId !== currentAccount.id) {
                DebugUtils.log('没有权限取消发布此关卡', 'error');
                return false;
            }

            level.isPublic = false;
            const success = await this.saveLevel(level);

            if (success) {
                DebugUtils.log(`关卡取消发布成功: ${level.name}`);
            }

            return success;
        } catch (error) {
            DebugUtils.log(`取消发布关卡失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 导出关卡数据
     * @param {string} levelId - 关卡ID
     * @returns {Promise<string|null>} 导出的JSON字符串或null
     */
    async exportLevel(levelId) {
        try {
            const level = await this.loadLevel(levelId);
            if (!level) {
                DebugUtils.log('关卡不存在', 'error');
                return null;
            }

            const exportData = {
                version: '1.0.0',
                exportTime: Date.now(),
                level: level
            };

            return JSON.stringify(exportData, null, 2);
        } catch (error) {
            DebugUtils.log(`导出关卡失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 导入关卡数据
     * @param {string} importString - 导入的JSON字符串
     * @returns {Promise<string|null>} 导入的关卡ID或null
     */
    async importLevel(importString) {
        try {
            const importData = JSON.parse(importString);

            if (!importData.level || !importData.version) {
                DebugUtils.log('导入数据格式无效', 'error');
                return null;
            }

            const accountManager = window.accountManager;
            const currentAccount = await accountManager.getCurrentAccount();

            if (!currentAccount) {
                DebugUtils.log('没有当前账号，无法导入关卡', 'error');
                return null;
            }

            // 生成新的关卡ID
            const newLevelId = this.generateLevelId();
            const level = {
                ...importData.level,
                id: newLevelId,
                author: currentAccount.name,
                authorId: currentAccount.id,
                createdAt: Date.now(),
                updatedAt: Date.now(),
                playCount: 0,
                rating: 0,
                ratingCount: 0,
                isPublic: false // 导入的关卡默认为私有
            };

            const success = await this.saveLevel(level);

            if (success) {
                DebugUtils.log(`关卡导入成功: ${level.name} (${newLevelId})`);
                return newLevelId;
            } else {
                return null;
            }
        } catch (error) {
            DebugUtils.log(`导入关卡失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 复制关卡
     * @param {string} levelId - 源关卡ID
     * @param {string} newName - 新关卡名称
     * @returns {Promise<string|null>} 新关卡ID或null
     */
    async duplicateLevel(levelId, newName) {
        try {
            const sourceLevel = await this.loadLevel(levelId);
            if (!sourceLevel) {
                DebugUtils.log('源关卡不存在', 'error');
                return null;
            }

            const accountManager = window.accountManager;
            const currentAccount = await accountManager.getCurrentAccount();

            if (!currentAccount) {
                DebugUtils.log('没有当前账号，无法复制关卡', 'error');
                return null;
            }

            // 创建新关卡
            const newLevelId = this.generateLevelId();
            const now = Date.now();

            const newLevel = {
                ...sourceLevel,
                id: newLevelId,
                name: newName || `${sourceLevel.name} - 副本`,
                author: currentAccount.name,
                authorId: currentAccount.id,
                createdAt: now,
                updatedAt: now,
                playCount: 0,
                rating: 0,
                ratingCount: 0,
                isPublic: false
            };

            const success = await this.saveLevel(newLevel);

            if (success) {
                DebugUtils.log(`关卡复制成功: ${newLevel.name} (${newLevelId})`);
                return newLevelId;
            } else {
                return null;
            }
        } catch (error) {
            DebugUtils.log(`复制关卡失败: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * 更新关卡元数据
     */
    async updateLevelMetadata() {
        try {
            const allLevels = await this.getAllLevels();

            const metadata = {
                totalLevels: allLevels.length,
                publicLevels: allLevels.filter(level => level.isPublic).length,
                difficulties: {},
                lastUpdated: Date.now()
            };

            // 统计各难度关卡数量
            Object.values(this.difficulties).forEach(difficulty => {
                metadata.difficulties[difficulty] = allLevels.filter(
                    level => level.difficulty === difficulty
                ).length;
            });

            await this.kvService.put(this.storageKeys.levelMetadata, metadata);
        } catch (error) {
            DebugUtils.log(`更新关卡元数据失败: ${error.message}`, 'error');
        }
    }
}

// 创建全局关卡管理器实例
window.levelManager = new LevelManager();
